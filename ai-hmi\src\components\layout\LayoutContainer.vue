<template>
  <div class="layout-container">
    <slot />
  </div>
</template>

<script>
import { provide, ref, watch, onMounted, onUnmounted } from 'vue'
import { useLayoutStore } from '@/store/modules/layout'

export default {
  name: 'LayoutContainer',
  props: {
    // 初始布局
    initialLayout: {
      type: String,
      default: 'default'
    },
    
    // 是否启用响应式
    responsive: {
      type: Boolean,
      default: true
    },
    
    // 是否启用动画
    animated: {
      type: Boolean,
      default: true
    },
    
    // 动画持续时间
    animationDuration: {
      type: Number,
      default: 500
    }
  },
  
  setup(props) {
    const layoutStore = useLayoutStore()
    const currentLayout = ref(props.initialLayout)
    
    // 布局上下文
    const layoutContext = ref({
      layout: currentLayout.value,
      isTransitioning: false,
      screenInfo: {
        width: window.innerWidth,
        height: window.innerHeight,
        deviceType: getDeviceType(window.innerWidth)
      }
    })
    
    // 提供布局上下文
    provide('layoutContext', layoutContext)
    
    // 获取设备类型
    function getDeviceType(width) {
      if (width <= 768) return 'mobile'
      if (width <= 1024) return 'tablet'
      if (width <= 1440) return 'desktop'
      return 'large'
    }
    
    // 处理窗口大小变化
    const handleResize = () => {
      const width = window.innerWidth
      const height = window.innerHeight
      
      layoutContext.value.screenInfo = {
        width,
        height,
        deviceType: getDeviceType(width)
      }
      
      if (props.responsive) {
        layoutStore.updateScreenSize(width)
      }
    }
    
    // 切换布局
    const switchLayout = (newLayout) => {
      if (newLayout === currentLayout.value) return
      
      layoutContext.value.isTransitioning = true
      
      setTimeout(() => {
        currentLayout.value = newLayout
        layoutContext.value.layout = newLayout
        layoutStore.switchLayout(newLayout)
        
        setTimeout(() => {
          layoutContext.value.isTransitioning = false
        }, props.animationDuration)
      }, props.animated ? props.animationDuration / 2 : 0)
    }
    
    // 暴露切换布局方法
    provide('switchLayout', switchLayout)
    
    // 监听布局变化
    watch(() => props.initialLayout, (newLayout) => {
      if (newLayout !== currentLayout.value) {
        switchLayout(newLayout)
      }
    })
    
    // 生命周期
    onMounted(() => {
      // 初始化屏幕尺寸
      handleResize()
      
      // 监听窗口大小变化
      window.addEventListener('resize', handleResize)
      
      // 初始化布局
      layoutStore.switchLayout(currentLayout.value)
    })
    
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)
    })
    
    return {
      switchLayout
    }
  }
}
</script>

<style scoped>
.layout-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background: transparent;
}

/* 布局切换动画 */
.layout-container.transitioning {
  pointer-events: none;
}

/* 全局过渡效果 */
.layout-container :deep(.layout-transition-enter-active),
.layout-container :deep(.layout-transition-leave-active) {
  transition: all 0.5s ease;
}

.layout-container :deep(.layout-transition-enter-from) {
  opacity: 0;
  transform: scale(0.9);
}

.layout-container :deep(.layout-transition-leave-to) {
  opacity: 0;
  transform: scale(1.1);
}

.layout-container :deep(.layout-transition-enter-to),
.layout-container :deep(.layout-transition-leave-from) {
  opacity: 1;
  transform: scale(1);
}
</style>