// Layout Components Export
export { default as GridLayout } from './GridLayout.vue'
export { default as GridItem } from './GridItem.vue'
export { default as LayoutContainer } from './LayoutContainer.vue'

// Layout Composables
export { useGridLayout } from '../composables/useGridLayout'
export { useResponsiveLayout } from '../composables/useResponsiveLayout'
export { useLayoutAnimation } from '../composables/useLayoutAnimation'

// Layout Utilities
export { gridPositionCalculator } from '../utils/gridPositionCalculator'
export { layoutValidator } from '../utils/layoutValidator'
export { themeManager } from '../utils/themeManager'

// Layout Constants
export const LAYOUT_MODES = {
  DEFAULT: 'default',
  FAMILY: 'family',
  FOCUS: 'focus',
  ENTERTAINMENT: 'entertainment',
  MINIMAL: 'minimal',
  IMMERSIVE: 'immersive',
  DRIVING: 'driving',
  PARKING: 'parking',
  EMERGENCY: 'emergency'
}

export const COMPONENT_SIZES = {
  // 基础尺寸
  SMALL: 'small',
  MEDIUM: 'medium',
  LARGE: 'large',
  FULL: 'full',
  CUSTOM: 'custom',
  
  // 特定组件尺寸
  DYNAMIC_ISLAND: 'dynamicIsland',
  VPA_WIDGET: 'vpaWidget',
  KID_EDUCATION: 'kidEducation',
  MUSIC: 'music',
  TODO: 'todo',
  PEDIA: 'pedia',
  ORDER_STATUS: 'orderStatus',
  NEWS: 'news',
  ENTERTAINMENT: 'entertainment',
  FATIGUE_WARNING: 'fatigueWarning',
  EMERGENCY_CONTACT: 'emergencyContact',
  FIRST_AID: 'firstAid'
}

export const THEME_TYPES = {
  GLASS: 'glass',
  SOLID: 'solid',
  MINIMAL: 'minimal',
  GRADIENT: 'gradient'
}

export const GRID_CONFIG = {
  DEFAULT_COLUMNS: 16,
  DEFAULT_ROWS: 9,
  DEFAULT_GAP: 'min(1vw, 10px)',
  DEFAULT_PADDING: 'min(1vw, 10px)',
  
  MOBILE_COLUMNS: 8,
  MOBILE_ROWS: 12,
  
  TABLET_COLUMNS: 12,
  TABLET_ROWS: 10
}

// Layout Helpers
export const createGridPosition = (x, y, width, height) => ({
  x, y, width, height
})

export const createThemeColors = (primary, secondary, background, text) => ({
  primary, secondary, background, text
})

export const getComponentSize = (componentType) => {
  const sizeMap = {
    [COMPONENT_SIZES.DYNAMIC_ISLAND]: { width: 16, height: 1 },
    [COMPONENT_SIZES.VPA_WIDGET]: { width: 2, height: 2 },
    [COMPONENT_SIZES.KID_EDUCATION]: { width: 8, height: 8 },
    [COMPONENT_SIZES.MUSIC]: { width: 8, height: 8 },
    [COMPONENT_SIZES.TODO]: { width: 8, height: 3 },
    [COMPONENT_SIZES.PEDIA]: { width: 8, height: 3 },
    [COMPONENT_SIZES.ORDER_STATUS]: { width: 4, height: 2 },
    [COMPONENT_SIZES.NEWS]: { width: 8, height: 4 },
    [COMPONENT_SIZES.ENTERTAINMENT]: { width: 16, height: 5 },
    [COMPONENT_SIZES.FATIGUE_WARNING]: { width: 8, height: 4 },
    [COMPONENT_SIZES.EMERGENCY_CONTACT]: { width: 8, height: 6 },
    [COMPONENT_SIZES.FIRST_AID]: { width: 8, height: 6 },
    [COMPONENT_SIZES.SMALL]: { width: 4, height: 2 },
    [COMPONENT_SIZES.MEDIUM]: { width: 8, height: 4 },
    [COMPONENT_SIZES.LARGE]: { width: 8, height: 8 },
    [COMPONENT_SIZES.FULL]: { width: 16, height: 9 }
  }
  
  return sizeMap[componentType] || { width: 4, height: 2 }
}