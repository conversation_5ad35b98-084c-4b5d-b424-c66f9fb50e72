<template>
  <div class="vpa-avatar-widget" :class="[`size-${size}`, `mode-${currentMode}`]">
    <!-- VPA头像容器 -->
    <div class="avatar-container" @click="handleAvatarClick">
      <!-- 头像显示区域 -->
      <div class="avatar-display">
        <div v-if="currentAnimationResource" class="avatar-animation">
          <!-- 动画资源容器 -->
          <div 
            ref="animationContainer"
            class="animation-container"
            :class="currentAnimationState"
          >
            <img 
              v-if="isImageAnimation"
              :src="currentAnimationResource"
              :alt="currentAnimationState"
              class="animation-image"
              :style="animationStyle"
              @load="handleAnimationLoad"
              @error="handleAnimationError"
            />
            <div v-else class="animation-placeholder">
              <i class="fas fa-user-circle avatar-icon"></i>
            </div>
          </div>
        </div>
        
        <div v-else class="avatar-fallback">
          <i class="fas fa-user-circle avatar-icon"></i>
        </div>
        
        <!-- 状态指示器 -->
        <div class="status-indicator" :class="currentAnimationState">
          <div class="status-dot"></div>
        </div>
        
        <!-- 语音波形动画 -->
        <div v-if="showVoiceWave" class="voice-wave">
          <div class="wave" v-for="i in 4" :key="i" :style="{ animationDelay: `${i * 0.1}s` }"></div>
        </div>
      </div>
      
      <!-- 交互提示 -->
      <div v-if="showHint" class="interaction-hint">
        <span>{{ currentHint }}</span>
      </div>
    </div>
    
    <!-- 快速操作按钮 -->
    <div v-if="size !== 'small'" class="quick-actions">
      <button 
        v-for="action in quickActions" 
        :key="action.id"
        @click="handleQuickAction(action)"
        class="action-btn"
        :title="action.label"
      >
        <i :class="action.icon"></i>
      </button>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useVpaStore } from '@/store/modules/vpa'
import { vpaAnimationManager } from '@/components/utils/vpaAnimationManager'

export default {
  name: 'VPAAvatarWidget',
  props: {
    size: {
      type: String,
      default: 'medium',
      validator: value => ['small', 'medium', 'large'].includes(value)
    },
    position: {
      type: Object,
      default: () => ({ x: 1, y: 1 })
    },
    theme: {
      type: String,
      default: 'glass'
    },
    themeColors: {
      type: Object,
      default: () => ({
        primary: '#667eea',
        secondary: '#764ba2',
        background: 'rgba(102, 126, 234, 0.1)',
        text: '#ffffff'
      })
    },
    showQuickActions: {
      type: Boolean,
      default: true
    }
  },
  
  emits: ['avatar-click', 'mode-changed', 'animation-changed', 'quick-action'],
  
  setup(props, { emit }) {
    const vpaStore = useVpaStore()
    
    // 响应式状态
    const showHint = ref(false)
    const showVoiceWave = ref(false)
    const hintTimer = ref(null)
    const animationContainer = ref(null)
    const animationLoaded = ref(false)
    const animationError = ref(false)
    
    // 计算属性
    const currentMode = computed(() => vpaStore.currentMode)
    const currentAnimationState = computed(() => vpaStore.currentAnimationState)
    const currentAnimationConfig = computed(() => vpaStore.currentAnimationConfig)
    
    const currentAnimationResource = computed(() => {
      const animationConfig = vpaStore.currentAnimationConfig
      if (animationConfig && animationConfig.resource) {
        try {
          // 使用动态导入来处理图片资源
          return new URL(`../assets/${animationConfig.resource}`, import.meta.url).href
        } catch (error) {
          console.warn('动画资源加载失败:', error)
          return null
        }
      }
      return null
    })

    // 判断是否为图片动画
    const isImageAnimation = computed(() => {
      const resource = currentAnimationResource.value
      return resource && (resource.endsWith('.gif') || resource.endsWith('.png') || resource.endsWith('.jpg') || resource.endsWith('.jpeg'))
    })

    // 动画样式
    const animationStyle = computed(() => {
      const animationConfig = vpaStore.currentAnimationConfig
      if (!animationConfig) return {}
      
      return {
        animation: animationConfig.loop ? 'infinite' : '1',
        opacity: animationLoaded.value ? 1 : 0,
        transition: 'opacity 0.3s ease'
      }
    })
    
    const currentHint = computed(() => {
      const hints = {
        companion: '我在这里陪伴您',
        interaction: '点击与我交流',
        hidden: ''
      }
      return hints[currentMode.value] || '点击与我互动'
    })
    
    const quickActions = computed(() => {
      if (!props.showQuickActions) return []
      
      return [
        {
          id: 'voice',
          label: '语音交互',
          icon: 'fas fa-microphone'
        },
        {
          id: 'settings',
          label: '设置',
          icon: 'fas fa-cog'
        },
        {
          id: 'help',
          label: '帮助',
          icon: 'fas fa-question-circle'
        }
      ]
    })
    
    // 方法
    const handleAvatarClick = () => {
      const clickData = {
        mode: currentMode.value,
        animationState: currentAnimationState.value,
        timestamp: Date.now()
      }
      
      emit('avatar-click', clickData)
      
      // 触发交互动画
      if (currentAnimationState.value === 'idle') {
        vpaStore.setAnimation('greeting')
        setTimeout(() => {
          vpaStore.setAnimation('idle')
        }, 3000)
      }
      
      // 显示语音波形
      showVoiceWave.value = true
      setTimeout(() => {
        showVoiceWave.value = false
      }, 2000)
    }
    
    const handleQuickAction = (action) => {
      emit('quick-action', action)
      
      // 根据操作类型触发相应动画
      switch (action.id) {
        case 'voice':
          vpaStore.setAnimation('listening')
          showVoiceWave.value = true
          break
        case 'settings':
          vpaStore.setAnimation('thinking')
          break
        case 'help':
          vpaStore.setAnimation('talking')
          break
      }
    }
    
    const showInteractionHint = () => {
      showHint.value = true
      hintTimer.value = setTimeout(() => {
        showHint.value = false
      }, 3000)
    }
    
    const hideInteractionHint = () => {
      showHint.value = false
      if (hintTimer.value) {
        clearTimeout(hintTimer.value)
        hintTimer.value = null
      }
    }
    
    // 生命周期
    onMounted(() => {
      console.log('VPA头像组件已加载')
      
      // 定期显示交互提示
      if (props.size !== 'small') {
        setInterval(() => {
          if (currentMode.value === 'companion' && !showHint.value) {
            showInteractionHint()
          }
        }, 30000) // 30秒显示一次提示
      }
    })
    
    onUnmounted(() => {
      hideInteractionHint()
    })
    
    return {
      currentMode,
      currentAnimationState,
      currentAnimationConfig,
      currentAnimationResource,
      currentHint,
      quickActions,
      showHint,
      showVoiceWave,
      handleAvatarClick,
      handleQuickAction,
      showInteractionHint,
      hideInteractionHint
    }
  }
}
</script>

<style scoped>
.vpa-avatar-widget {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border-radius: 15px;
  background: var(--card-background, rgba(255, 255, 255, 0.1));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.vpa-avatar-widget:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 尺寸变体 */
.size-small {
  width: 80px;
  height: 80px;
  padding: 8px;
}

.size-medium {
  width: 120px;
  height: 140px;
  padding: 10px;
}

.size-large {
  width: 160px;
  height: 200px;
  padding: 15px;
}

/* 头像容器 */
.avatar-container {
  position: relative;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.avatar-container:hover {
  transform: scale(1.05);
}

.avatar-display {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  background: linear-gradient(135deg, var(--card-primary-color, #667eea) 0%, var(--card-secondary-color, #764ba2) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.size-small .avatar-display {
  width: 50px;
  height: 50px;
}

.size-large .avatar-display {
  width: 80px;
  height: 80px;
}

.avatar-icon {
  font-size: 40px;
  color: white;
  transition: all 0.3s ease;
}

.size-small .avatar-icon {
  font-size: 30px;
}

.size-large .avatar-icon {
  font-size: 50px;
}

/* 动画状态 */
.animation-placeholder.idle .avatar-icon {
  animation: breathe 3s ease-in-out infinite;
}

.animation-placeholder.talking .avatar-icon {
  animation: talking 0.5s ease-in-out infinite;
}

.animation-placeholder.listening .avatar-icon {
  animation: pulse 1s ease-in-out infinite;
}

.animation-placeholder.thinking .avatar-icon {
  animation: thinking 2s ease-in-out infinite;
}

.animation-placeholder.greeting .avatar-icon {
  animation: bounce 0.6s ease-in-out;
}

/* 状态指示器 */
.status-indicator {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.status-dot {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #4CAF50;
  animation: statusPulse 2s ease-in-out infinite;
}

.status-indicator.talking .status-dot {
  background: #2196F3;
  animation: statusBlink 0.5s ease-in-out infinite;
}

.status-indicator.listening .status-dot {
  background: #FF9800;
  animation: statusPulse 1s ease-in-out infinite;
}

.status-indicator.thinking .status-dot {
  background: #9C27B0;
  animation: statusRotate 2s linear infinite;
}

/* 语音波形 */
.voice-wave {
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 2px;
  align-items: flex-end;
}

.wave {
  width: 3px;
  height: 8px;
  background: var(--card-primary-color, #667eea);
  border-radius: 2px;
  animation: wave 1s infinite ease-in-out;
}

/* 交互提示 */
.interaction-hint {
  position: absolute;
  top: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 11px;
  white-space: nowrap;
  opacity: 0;
  animation: hintFadeIn 0.3s ease forwards;
}

.interaction-hint::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.8);
}

/* 快速操作按钮 */
.quick-actions {
  display: flex;
  gap: 5px;
  margin-top: 5px;
}

.action-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: var(--card-text-color, #ffffff);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: var(--card-primary-color, #667eea);
  transform: scale(1.1);
}

/* 动画定义 */
@keyframes breathe {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes talking {
  0%, 100% { transform: scaleY(1); }
  50% { transform: scaleY(1.1); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes thinking {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(5deg); }
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes statusBlink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

@keyframes statusRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes hintFadeIn {
  0% { opacity: 0; transform: translateX(-50%) translateY(-5px); }
  100% { opacity: 1; transform: translateX(-50%) translateY(0); }
}

@keyframes wave {
  0%, 100% { height: 8px; }
  50% { height: 16px; }
}
</style>
