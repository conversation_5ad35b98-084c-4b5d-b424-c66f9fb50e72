import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useLayoutStore } from '@/store/modules/layout'

/**
 * 响应式布局管理组合式函数
 * 处理不同设备尺寸下的布局适配
 */
export function useResponsiveLayout() {
  const layoutStore = useLayoutStore()
  const screenSize = ref({
    width: window.innerWidth,
    height: window.innerHeight
  })
  
  const deviceType = computed(() => {
    const width = screenSize.value.width
    if (width <= 768) return 'mobile'
    if (width <= 1024) return 'tablet'
    if (width <= 1440) return 'desktop'
    return 'large'
  })
  
  // 响应式网格配置
  const responsiveGridConfig = computed(() => {
    const type = deviceType.value
    const configs = {
      mobile: {
        columns: 8,
        rows: 12,
        gap: '5px',
        padding: '5px'
      },
      tablet: {
        columns: 12,
        rows: 10,
        gap: '8px',
        padding: '8px'
      },
      desktop: {
        columns: 16,
        rows: 9,
        gap: 'min(1vw, 10px)',
        padding: 'min(1vw, 10px)'
      },
      large: {
        columns: 20,
        rows: 12,
        gap: 'min(1vw, 12px)',
        padding: 'min(1vw, 12px)'
      }
    }
    
    return configs[type] || configs.desktop
  })
  
  // 响应式组件尺寸
  const responsiveComponentSizes = computed(() => {
    const type = deviceType.value
    const baseSizes = {
      // 基础尺寸
      small: { width: 2, height: 2 },
      medium: { width: 4, height: 3 },
      large: { width: 6, height: 4 },
      full: { width: 16, height: 9 },
      
      // 特定组件尺寸
      dynamicIsland: { width: 16, height: 1 },
      vpaWidget: { width: 2, height: 2 },
      kidEducation: { width: 8, height: 8 },
      music: { width: 8, height: 8 },
      todo: { width: 8, height: 3 },
      pedia: { width: 8, height: 3 },
      orderStatus: { width: 4, height: 2 },
      news: { width: 8, height: 4 },
      entertainment: { width: 16, height: 5 },
      fatigueWarning: { width: 8, height: 4 },
      emergencyContact: { width: 8, height: 6 },
      firstAid: { width: 8, height: 6 }
    }
    
    // 根据设备类型调整尺寸
    const scaleFactor = {
      mobile: 0.6,
      tablet: 0.8,
      desktop: 1.0,
      large: 1.2
    }
    
    const scale = scaleFactor[type]
    const scaledSizes = {}
    
    Object.entries(baseSizes).forEach(([key, size]) => {
      scaledSizes[key] = {
        width: Math.max(1, Math.round(size.width * scale)),
        height: Math.max(1, Math.round(size.height * scale))
      }
    })
    
    return scaledSizes
  })
  
  // 检查是否为移动设备
  const isMobile = computed(() => deviceType.value === 'mobile')
  
  // 检查是否为平板设备
  const isTablet = computed(() => deviceType.value === 'tablet')
  
  // 检查是否为桌面设备
  const isDesktop = computed(() => ['desktop', 'large'].includes(deviceType.value))
  
  // 检查是否为横屏
  const isLandscape = computed(() => screenSize.value.width > screenSize.value.height)
  
  // 检查是否为竖屏
  const isPortrait = computed(() => screenSize.value.width <= screenSize.value.height)
  
  // 获取适合的布局模式
  const getOptimalLayout = (availableLayouts) => {
    const type = deviceType.value
    
    // 根据设备类型推荐布局
    if (type === 'mobile') {
      return availableLayouts.find(layout => 
        layout.includes('minimal') || layout.includes('focus')
      ) || 'minimal'
    }
    
    if (type === 'tablet') {
      return availableLayouts.find(layout => 
        layout.includes('family') || layout.includes('entertainment')
      ) || 'family'
    }
    
    return availableLayouts[0] || 'default'
  }
  
  // 自适应组件位置
  const adaptComponentPosition = (componentType, position) => {
    const gridConfig = responsiveGridConfig.value
    const componentSize = responsiveComponentSizes.value[componentType] || 
                         responsiveComponentSizes.value.medium
    
    // 确保组件不超出网格边界
    const maxX = gridConfig.columns - componentSize.width + 1
    const maxY = gridConfig.rows - componentSize.height + 1
    
    const adaptedPosition = {
      x: Math.min(position.x || 1, maxX),
      y: Math.min(position.y || 1, maxY)
    }
    
    return adaptedPosition
  }
  
  // 处理窗口大小变化
  const handleResize = () => {
    screenSize.value = {
      width: window.innerWidth,
      height: window.innerHeight
    }
    
    // 更新store中的屏幕尺寸
    layoutStore.updateScreenSize(screenSize.value.width)
  }
  
  // 监听屏幕方向变化
  const handleOrientationChange = () => {
    // 延迟处理以确保尺寸已更新
    setTimeout(handleResize, 100)
  }
  
  onMounted(() => {
    // 初始化屏幕尺寸
    handleResize()
    
    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
    
    // 监听屏幕方向变化
    window.addEventListener('orientationchange', handleOrientationChange)
  })
  
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    window.removeEventListener('orientationchange', handleOrientationChange)
  })
  
  return {
    screenSize,
    deviceType,
    responsiveGridConfig,
    responsiveComponentSizes,
    isMobile,
    isTablet,
    isDesktop,
    isLandscape,
    isPortrait,
    getOptimalLayout,
    adaptComponentPosition
  }
}

/**
 * 默认导出
 */
export default useResponsiveLayout