/**
 * 网格位置计算器
 * 提供16x9网格系统的位置计算和布局算法
 */
export class GridPositionCalculator {
  constructor(gridConfig = {}) {
    this.columns = gridConfig.columns || 16
    this.rows = gridConfig.rows || 9
    this.gap = gridConfig.gap || 'min(1vw, 10px)'
    this.padding = gridConfig.padding || 'min(1vw, 10px)'
  }
  
  /**
   * 计算网格位置样式
   */
  calculatePosition(x, y, width, height) {
    return {
      gridColumn: `${x} / span ${width}`,
      gridRow: `${y} / span ${height}`
    }
  }
  
  /**
   * 验证位置是否有效
   */
  isValidPosition(x, y, width, height) {
    return (
      x >= 1 &&
      y >= 1 &&
      x + width - 1 <= this.columns &&
      y + height - 1 <= this.rows
    )
  }
  
  /**
   * 检查位置是否被占用
   */
  isPositionOccupied(x, y, width, height, occupiedPositions) {
    for (let pos of occupiedPositions) {
      if (this.isOverlapping(x, y, width, height, pos.x, pos.y, pos.width, pos.height)) {
        return true
      }
    }
    return false
  }
  
  /**
   * 检查两个区域是否重叠
   */
  isOverlapping(x1, y1, w1, h1, x2, y2, w2, h2) {
    return !(x1 + w1 - 1 < x2 || x2 + w2 - 1 < x1 || 
             y1 + h1 - 1 < y2 || y2 + h2 - 1 < y1)
  }
  
  /**
   * 查找第一个可用位置
   */
  findFirstAvailablePosition(width, height, occupiedPositions) {
    for (let y = 1; y <= this.rows - height + 1; y++) {
      for (let x = 1; x <= this.columns - width + 1; x++) {
        if (!this.isPositionOccupied(x, y, width, height, occupiedPositions)) {
          return { x, y }
        }
      }
    }
    return null
  }
  
  /**
   * 自动排列组件
   */
  autoArrange(components) {
    const arranged = []
    const occupiedPositions = []
    
    // 按尺寸从大到小排序
    const sortedComponents = [...components].sort((a, b) => {
      const aSize = (a.width || 1) * (a.height || 1)
      const bSize = (b.width || 1) * (b.height || 1)
      return bSize - aSize
    })
    
    for (let component of sortedComponents) {
      const width = component.width || 1
      const height = component.height || 1
      
      // 尝试找到合适的位置
      let position = this.findFirstAvailablePosition(width, height, occupiedPositions)
      
      if (!position) {
        // 如果找不到位置，尝试缩小尺寸
        position = this.findFirstAvailablePosition(
          Math.max(1, Math.floor(width * 0.8)),
          Math.max(1, Math.floor(height * 0.8)),
          occupiedPositions
        )
      }
      
      if (position) {
        arranged.push({
          ...component,
          x: position.x,
          y: position.y,
          width: width,
          height: height
        })
        
        occupiedPositions.push({
          x: position.x,
          y: position.y,
          width: width,
          height: height
        })
      }
    }
    
    return arranged
  }
  
  /**
   * 优化布局以减少空白空间
   */
  optimizeLayout(components) {
    const arranged = this.autoArrange(components)
    
    // 尝试紧凑化布局
    let optimized = false
    let attempts = 0
    const maxAttempts = 10
    
    while (!optimized && attempts < maxAttempts) {
      optimized = true
      attempts++
      
      for (let i = 0; i < arranged.length; i++) {
        const component = arranged[i]
        
        // 尝试向左移动
        if (component.x > 1) {
          const newX = component.x - 1
          if (!this.isPositionOccupied(
            newX, component.y, component.width, component.height,
            arranged.filter(c => c !== component).map(c => ({
              x: c.x, y: c.y, width: c.width, height: c.height
            }))
          )) {
            component.x = newX
            optimized = false
          }
        }
        
        // 尝试向上移动
        if (component.y > 1) {
          const newY = component.y - 1
          if (!this.isPositionOccupied(
            component.x, newY, component.width, component.height,
            arranged.filter(c => c !== component).map(c => ({
              x: c.x, y: c.y, width: c.width, height: c.height
            }))
          )) {
            component.y = newY
            optimized = false
          }
        }
      }
    }
    
    return arranged
  }
  
  /**
   * 计算网格的像素尺寸
   */
  calculatePixelSize(containerWidth, containerHeight) {
    const gapPixels = parseFloat(this.gap) || 10
    const paddingPixels = parseFloat(this.padding) || 10
    
    const availableWidth = containerWidth - 2 * paddingPixels - (this.columns - 1) * gapPixels
    const availableHeight = containerHeight - 2 * paddingPixels - (this.rows - 1) * gapPixels
    
    return {
      cellWidth: availableWidth / this.columns,
      cellHeight: availableHeight / this.rows,
      gap: gapPixels,
      padding: paddingPixels
    }
  }
  
  /**
   * 将像素坐标转换为网格坐标
   */
  pixelsToGrid(pixelX, pixelY, containerWidth, containerHeight) {
    const pixelSize = this.calculatePixelSize(containerWidth, containerHeight)
    
    const gridX = Math.floor((pixelX - pixelSize.padding) / (pixelSize.cellWidth + pixelSize.gap)) + 1
    const gridY = Math.floor((pixelY - pixelSize.padding) / (pixelSize.cellHeight + pixelSize.gap)) + 1
    
    return {
      x: Math.max(1, Math.min(gridX, this.columns)),
      y: Math.max(1, Math.min(gridY, this.rows))
    }
  }
  
  /**
   * 将网格坐标转换为像素坐标
   */
  gridToPixels(gridX, gridY, containerWidth, containerHeight) {
    const pixelSize = this.calculatePixelSize(containerWidth, containerHeight)
    
    const pixelX = pixelSize.padding + (gridX - 1) * (pixelSize.cellWidth + pixelSize.gap)
    const pixelY = pixelSize.padding + (gridY - 1) * (pixelSize.cellHeight + pixelSize.gap)
    
    return {
      x: pixelX,
      y: pixelY,
      width: pixelSize.cellWidth,
      height: pixelSize.cellHeight
    }
  }
  
  /**
   * 计算组件的中心点
   */
  calculateCenter(x, y, width, height) {
    return {
      x: x + (width - 1) / 2,
      y: y + (height - 1) / 2
    }
  }
  
  /**
   * 计算两个组件之间的距离
   */
  calculateDistance(pos1, pos2) {
    const center1 = this.calculateCenter(pos1.x, pos1.y, pos1.width, pos1.height)
    const center2 = this.calculateCenter(pos2.x, pos2.y, pos2.width, pos2.height)
    
    return Math.sqrt(
      Math.pow(center2.x - center1.x, 2) + 
      Math.pow(center2.y - center1.y, 2)
    )
  }
  
  /**
   * 获取邻近组件
   */
  getAdjacentComponents(component, components, maxDistance = 2) {
    return components.filter(other => {
      if (other === component) return false
      
      const distance = this.calculateDistance(
        { x: component.x, y: component.y, width: component.width, height: component.height },
        { x: other.x, y: other.y, width: other.width, height: other.height }
      )
      
      return distance <= maxDistance
    })
  }
  
  /**
   * 生成布局建议
   */
  generateLayoutSuggestions(components, constraints = {}) {
    const suggestions = []
    
    // 紧凑布局建议
    const compactLayout = this.optimizeLayout(components)
    suggestions.push({
      type: 'compact',
      name: '紧凑布局',
      description: '最小化空白空间，提高空间利用率',
      layout: compactLayout
    })
    
    // 分组布局建议
    const groupedLayout = this.groupComponentsByType(components)
    suggestions.push({
      type: 'grouped',
      name: '分组布局',
      description: '按组件类型分组排列',
      layout: groupedLayout
    })
    
    // 优先级布局建议
    if (constraints.priorityOrder) {
      const priorityLayout = this.arrangeByPriority(components, constraints.priorityOrder)
      suggestions.push({
        type: 'priority',
        name: '优先级布局',
        description: '按优先级顺序排列组件',
        layout: priorityLayout
      })
    }
    
    return suggestions
  }
  
  /**
   * 按组件类型分组
   */
  groupComponentsByType(components) {
    const groups = {}
    
    // 按类型分组
    components.forEach(component => {
      const type = component.type || 'default'
      if (!groups[type]) {
        groups[type] = []
      }
      groups[type].push(component)
    })
    
    const arranged = []
    let currentY = 1
    
    // 按组排列
    Object.values(groups).forEach(group => {
      const groupArranged = this.autoArrange(group)
      groupArranged.forEach(component => {
        component.y = currentY
        arranged.push(component)
      })
      currentY += Math.max(...group.map(c => c.height || 1)) + 1
    })
    
    return arranged
  }
  
  /**
   * 按优先级排列
   */
  arrangeByPriority(components, priorityOrder) {
    const arranged = []
    let currentY = 1
    
    priorityOrder.forEach(priority => {
      const priorityComponents = components.filter(c => c.priority === priority)
      const priorityArranged = this.autoArrange(priorityComponents)
      
      priorityArranged.forEach(component => {
        component.y = currentY
        arranged.push(component)
      })
      
      currentY += Math.max(...priorityComponents.map(c => c.height || 1), 1) + 1
    })
    
    return arranged
  }
}

/**
 * 默认导出实例
 */
export const gridPositionCalculator = new GridPositionCalculator()

/**
 * 默认导出类
 */
export default GridPositionCalculator