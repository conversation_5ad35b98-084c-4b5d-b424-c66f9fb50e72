<template>
  <BaseCard
    card-type="schedule-assistant"
    size="large"
    :position="position"
    :theme="theme"
    :theme-colors="themeColors"
    :title="'AI日程助理'"
    :icon="'fas fa-calendar-alt'"
    :clickable="true"
    @click="handleCardClick"
    class="schedule-assistant-card"
  >
    <div class="schedule-content">
      <!-- 当前行程信息 -->
      <div class="current-trip">
        <div class="trip-header">
          <h4>当前行程</h4>
          <span class="trip-status" :class="tripStatus">{{ tripStatusText }}</span>
        </div>
        
        <div class="trip-details">
          <div class="destination">
            <i class="fas fa-map-marker-alt"></i>
            <span>{{ currentTrip.destination }}</span>
          </div>
          <div class="route-info">
            <div class="distance">{{ currentTrip.distance }}</div>
            <div class="duration">{{ currentTrip.estimatedTime }}</div>
            <div class="traffic" :class="trafficStatus">{{ trafficText }}</div>
          </div>
        </div>
      </div>

      <!-- 智能建议 -->
      <div class="ai-suggestions">
        <h5>智能建议</h5>
        <div class="suggestion-list">
          <div 
            v-for="suggestion in aiSuggestions" 
            :key="suggestion.id"
            class="suggestion-item"
            @click="applySuggestion(suggestion)"
          >
            <i :class="suggestion.icon"></i>
            <span>{{ suggestion.text }}</span>
          </div>
        </div>
      </div>

      <!-- 今日日程预览 -->
      <div class="schedule-preview">
        <h5>今日日程</h5>
        <div class="schedule-items">
          <div 
            v-for="item in todaySchedule" 
            :key="item.id"
            class="schedule-item"
            :class="{ active: item.isCurrent }"
          >
            <div class="schedule-time">{{ item.time }}</div>
            <div class="schedule-info">
              <div class="schedule-title">{{ item.title }}</div>
              <div class="schedule-location">{{ item.location }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BaseCard>
</template>

<script>
import { ref, computed } from 'vue'
import BaseCard from '../BaseCard.vue'

export default {
  name: 'AIScheduleAssistantCard',
  components: { BaseCard },
  props: {
    position: {
      type: Object,
      default: () => ({ x: 1, y: 1 })
    },
    theme: {
      type: String,
      default: 'glass'
    },
    themeColors: {
      type: Object,
      default: () => ({
        primary: '#3498db',
        secondary: '#2ecc71',
        background: 'rgba(52, 152, 219, 0.1)',
        text: '#ffffff'
      })
    }
  },
  
  emits: ['card-click', 'suggestion-applied', 'schedule-updated'],
  
  setup(props, { emit }) {
    // 模拟数据
    const currentTrip = ref({
      destination: '北京市朝阳区CBD',
      distance: '12.5公里',
      estimatedTime: '25分钟',
      traffic: 'moderate'
    })
    
    const aiSuggestions = ref([
      { id: 1, icon: 'fas fa-route', text: '建议走三环路，避开拥堵' },
      { id: 2, icon: 'fas fa-coffee', text: '路过星巴克，预订咖啡？' },
      { id: 3, icon: 'fas fa-phone', text: '提前5分钟通知会议参与者' }
    ])
    
    const todaySchedule = ref([
      { id: 1, time: '09:00', title: '团队晨会', location: 'A座会议室', isCurrent: true },
      { id: 2, time: '11:00', title: '项目评审', location: 'B座会议室', isCurrent: false },
      { id: 3, time: '14:00', title: '客户拜访', location: '外部地点', isCurrent: false }
    ])
    
    // 计算属性
    const tripStatus = computed(() => {
      return currentTrip.value.traffic === 'heavy' ? 'warning' : 'normal'
    })
    
    const tripStatusText = computed(() => {
      const statusMap = {
        light: '畅通',
        moderate: '缓慢',
        heavy: '拥堵'
      }
      return statusMap[currentTrip.value.traffic] || '正常'
    })
    
    const trafficStatus = computed(() => currentTrip.value.traffic)
    const trafficText = computed(() => tripStatusText.value)
    
    // 方法
    const handleCardClick = () => {
      emit('card-click', 'schedule-assistant')
    }
    
    const applySuggestion = (suggestion) => {
      emit('suggestion-applied', suggestion)
      console.log('应用建议:', suggestion.text)
    }
    
    return {
      currentTrip,
      aiSuggestions,
      todaySchedule,
      tripStatus,
      tripStatusText,
      trafficStatus,
      trafficText,
      handleCardClick,
      applySuggestion
    }
  }
}
</script>

<style scoped>
.schedule-assistant-card {
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.1) 0%, rgba(46, 204, 113, 0.1) 100%);
}

.schedule-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
  height: 100%;
}

/* 当前行程 */
.current-trip {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 15px;
}

.trip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.trip-header h4 {
  margin: 0;
  color: var(--card-text-color, #ffffff);
  font-size: 14px;
}

.trip-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.trip-status.normal { background: #2ecc71; color: white; }
.trip-status.warning { background: #f39c12; color: white; }

.destination {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  color: var(--card-text-color, #ffffff);
  font-weight: 600;
}

.route-info {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: var(--card-text-color, #ffffff);
  opacity: 0.8;
}

/* AI建议 */
.ai-suggestions h5 {
  margin: 0 0 10px 0;
  color: var(--card-text-color, #ffffff);
  font-size: 13px;
}

.suggestion-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
  color: var(--card-text-color, #ffffff);
}

.suggestion-item:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 日程预览 */
.schedule-preview h5 {
  margin: 0 0 10px 0;
  color: var(--card-text-color, #ffffff);
  font-size: 13px;
}

.schedule-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.schedule-item {
  display: flex;
  gap: 12px;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.schedule-item.active {
  background: var(--card-primary-color, #3498db);
}

.schedule-time {
  font-size: 11px;
  font-weight: 600;
  color: var(--card-text-color, #ffffff);
  min-width: 40px;
}

.schedule-info {
  flex: 1;
}

.schedule-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--card-text-color, #ffffff);
  margin-bottom: 2px;
}

.schedule-location {
  font-size: 10px;
  color: var(--card-text-color, #ffffff);
  opacity: 0.7;
}
</style>
