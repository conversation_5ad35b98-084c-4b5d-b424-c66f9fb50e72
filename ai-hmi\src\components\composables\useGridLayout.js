import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useLayoutStore } from '@/store/modules/layout'

/**
 * 网格布局管理组合式函数
 * 提供16x9网格系统的布局管理功能
 */
export function useGridLayout(initialLayout = 'default') {
  const layoutStore = useLayoutStore()
  const currentLayout = ref(initialLayout)
  const isTransitioning = ref(false)
  
  // 网格配置
  const gridConfig = computed(() => ({
    columns: layoutStore.gridColumns,
    rows: layoutStore.gridRows,
    gap: layoutStore.gridGap,
    padding: layoutStore.gridPadding
  }))
  
  // 组件位置映射
  const componentPositions = computed(() => layoutStore.componentPositions)
  
  // 切换布局
  const switchLayout = (newLayout) => {
    if (newLayout === currentLayout.value) return
    
    isTransitioning.value = true
    currentLayout.value = newLayout
    
    // 更新store
    layoutStore.switchLayout(newLayout)
    
    // 动画结束后重置状态
    setTimeout(() => {
      isTransitioning.value = false
    }, 500)
  }
  
  // 获取组件位置
  const getComponentPosition = (componentType, position = { x: 1, y: 1 }) => {
    const layout = layoutStore.getCurrentLayout()
    return layoutStore.getComponentPosition(componentType, position)
  }
  
  // 注册组件尺寸
  const registerComponentSize = (componentType, size) => {
    layoutStore.registerComponentSize(componentType, size)
  }
  
  // 计算网格位置
  const calculateGridPosition = (x, y, width, height) => {
    return {
      gridColumn: `${x} / span ${width}`,
      gridRow: `${y} / span ${height}`
    }
  }
  
  // 自动排列组件
  const autoArrangeComponents = (components) => {
    const layout = layoutStore.getCurrentLayout()
    return layoutStore.autoArrangeComponents(components, layout)
  }
  
  // 监听布局变化
  watch(() => layoutStore.currentLayout, (newLayout) => {
    if (newLayout !== currentLayout.value) {
      currentLayout.value = newLayout
    }
  })
  
  return {
    currentLayout,
    isTransitioning,
    gridConfig,
    componentPositions,
    switchLayout,
    getComponentPosition,
    registerComponentSize,
    calculateGridPosition,
    autoArrangeComponents
  }
}

/**
 * 默认导出
 */
export default useGridLayout