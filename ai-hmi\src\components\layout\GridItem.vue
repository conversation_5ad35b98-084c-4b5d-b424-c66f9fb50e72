<template>
  <div 
    :class="['grid-item', `size-${size}`, themeClass]"
    :style="itemStyles"
    @click="handleClick"
  >
    <slot />
  </div>
</template>

<script>
import { computed, inject } from 'vue'
import { useLayoutStore } from '@/store/modules/layout'

export default {
  name: 'GridItem',
  props: {
    // 组件尺寸类型
    size: {
      type: String,
      default: 'medium',
      validator: (value) => [
        'small', 'medium', 'large', 'full', 'custom',
        'dynamicIsland', 'vpaWidget', 'kidEducation', 'music',
        'todo', 'pedia', 'orderStatus', 'news', 'entertainment',
        'fatigueWarning', 'emergencyContact', 'firstAid'
      ].includes(value)
    },
    
    // 自定义尺寸
    customSize: {
      type: Object,
      default: () => ({ width: 4, height: 2 })
    },
    
    // 网格位置
    position: {
      type: Object,
      default: () => ({ x: 1, y: 1 })
    },
    
    // 主题
    theme: {
      type: String,
      default: 'glass',
      validator: (value) => ['glass', 'solid', 'minimal', 'gradient'].includes(value)
    },
    
    // 主题颜色
    themeColors: {
      type: Object,
      default: () => ({})
    },
    
    // 是否可点击
    clickable: {
      type: Boolean,
      default: false
    },
    
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    }
  },
  
  emits: ['click'],
  
  setup(props, { emit }) {
    const layoutStore = useLayoutStore()
    const layoutContext = inject('layoutContext', { layout: 'default' })
    
    // 项目样式
    const itemStyles = computed(() => {
      const baseStyles = {
        // 基础网格样式由GridLayout提供
      }
      
      // 获取网格位置样式
      let gridPosition = {}
      
      if (props.size === 'custom') {
        // 注册自定义尺寸
        layoutStore.registerComponentSize('custom', props.customSize)
        gridPosition = layoutStore.getComponentPosition('custom', props.position)
      } else {
        gridPosition = layoutStore.getComponentPosition(props.size, props.position)
      }
      
      // 主题颜色样式
      const themeStyles = {
        '--item-primary-color': props.themeColors.primary || '#4a90e2',
        '--item-secondary-color': props.themeColors.secondary || '#7ed321',
        '--item-background': props.themeColors.background || 'rgba(255, 255, 255, 0.1)',
        '--item-text-color': props.themeColors.text || '#ffffff'
      }
      
      return {
        ...baseStyles,
        ...gridPosition,
        ...themeStyles
      }
    })
    
    // 主题类名
    const themeClass = computed(() => `theme-${props.theme}`)
    
    // 处理点击事件
    const handleClick = () => {
      if (props.clickable && !props.disabled) {
        emit('click')
      }
    }
    
    return {
      itemStyles,
      themeClass,
      handleClick
    }
  }
}
</script>

<style scoped>
.grid-item {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

/* 主题样式 */
.theme-glass {
  background: var(--item-background, rgba(255, 255, 255, 0.1));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.theme-solid {
  background: var(--item-primary-color, #4a90e2);
  border: none;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.theme-minimal {
  background: transparent;
  border: 1px solid var(--item-primary-color, #4a90e2);
  box-shadow: none;
}

.theme-gradient {
  background: linear-gradient(135deg, 
    var(--item-primary-color, #4a90e2) 0%, 
    var(--item-secondary-color, #7ed321) 100%);
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* 可点击状态 */
.grid-item.clickable {
  cursor: pointer;
}

.grid-item.clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
}

/* 禁用状态 */
.grid-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 特定尺寸的样式调整 */
.size-dynamicIsland {
  z-index: 100;
}

.size-vpaWidget {
  z-index: 50;
}

.size-kidEducation,
.size-music {
  z-index: 10;
}

.size-emergencyContact,
.size-firstAid {
  z-index: 200; /* 紧急组件最高优先级 */
}

/* 响应式调整 */
@media (max-width: 768px) {
  .grid-item {
    border-radius: 10px;
  }
  
  .theme-glass {
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}
</style>