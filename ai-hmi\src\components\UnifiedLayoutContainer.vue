<template>
  <div class="unified-layout-container" :class="[currentLayout, currentTheme]">
    <!-- 16x9网格系统 -->
    <div class="grid-system">
      <!-- 动态区域 - 灵动岛 -->
      <div class="dynamic-zone" :style="getZoneStyle('dynamic')">
        <DynamicIsland 
          :current-scene="currentScene"
          :theme-colors="themeColors"
          @zone-click="handleZoneClick('dynamic')"
        />
      </div>

      <!-- 主要功能区 - 左侧8x8区域 -->
      <div class="primary-zone" :style="getZoneStyle('primary')">
        <component 
          :is="primaryComponent"
          v-if="primaryComponent"
          :current-scene="currentScene"
          :theme-colors="themeColors"
          @zone-click="handleZoneClick('primary')"
        />
      </div>

      <!-- 次要功能区 - 右侧8x3区域 -->
      <div class="secondary-zone" :style="getZoneStyle('secondary')">
        <component 
          :is="secondaryComponent"
          v-if="secondaryComponent"
          :current-scene="currentScene"
          :theme-colors="themeColors"
          @zone-click="handleZoneClick('secondary')"
        />
      </div>

      <!-- 辅助功能区 - 右侧8x3区域（次要下方） -->
      <div class="auxiliary-zone" :style="getZoneStyle('auxiliary')">
        <component 
          :is="auxiliaryComponent"
          v-if="auxiliaryComponent"
          :current-scene="currentScene"
          :theme-colors="themeColors"
          @zone-click="handleZoneClick('auxiliary')"
        />
      </div>

      <!-- VPA交互区 - 右下角2x2区域 -->
      <div class="vpa-zone" :style="getZoneStyle('vpa')">
        <VPAAvatarWidget
          :size="'small'"
          :position="{ x: 15, y: 7 }"
          :theme="'glass'"
          :theme-colors="themeColors"
          @avatar-click="handleVpaClick"
          @mode-changed="handleVpaModeChanged"
          @animation-changed="handleVpaAnimationChanged"
        />
      </div>
    </div>

    <!-- 布局切换指示器 -->
    <div class="layout-indicator" v-if="showIndicator">
      <div class="indicator-content">
        <i :class="getLayoutIcon"></i>
        <span>{{ getLayoutName }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useLayoutStore } from '@/store'
import DynamicIsland from './DynamicIsland.vue'
import VPAAvatarWidget from './vpa/VPAAvatarWidget.vue'

// 场景组件映射
const sceneComponents = {
  // 家庭出行模式
  family: {
    primary: 'KidEducationCard',
    secondary: 'PediaCard',
    auxiliary: null
  },
  // 专注通勤模式
  focus: {
    primary: 'MusicControlCard',
    secondary: 'TodoCard',
    auxiliary: 'OrderStatusCard'
  },
  // 娱乐等待模式
  entertainment: {
    primary: 'VideoPlayerCard',
    secondary: 'NewsCard',
    auxiliary: 'AmbientSoundCard'
  },
  // 极简雨夜模式
  minimal: {
    primary: null,
    secondary: null,
    auxiliary: 'MusicCard'
  },
  // 默认模式
  default: {
    primary: 'NavigationCard',
    secondary: 'WeatherCard',
    auxiliary: 'CalendarCard'
  }
}

export default {
  name: 'UnifiedLayoutContainer',
  components: {
    DynamicIsland,
    VPAAvatarWidget
  },
  props: {
    // 当前场景
    currentScene: {
      type: Object,
      required: true
    },
    // 主题颜色
    themeColors: {
      type: Object,
      default: null
    },
    // 显示指示器
    showIndicator: {
      type: Boolean,
      default: true
    },
    // 布局模式
    layoutMode: {
      type: String,
      default: 'auto' // auto, family, focus, entertainment, minimal
    }
  },
  emits: ['zone-click', 'vpa-click', 'layout-changed'],
  setup(props, { emit }) {
    const layoutStore = useLayoutStore()
    const currentLayout = ref('default')
    
    // 计算当前布局
    const computedLayout = computed(() => {
      if (props.layoutMode !== 'auto') {
        return props.layoutMode
      }
      
      // 根据场景ID自动选择布局
      const sceneId = props.currentScene?.id || 'default'
      if (sceneId.includes('family') || sceneId.includes('kid')) {
        return 'family'
      } else if (sceneId.includes('focus') || sceneId.includes('commute')) {
        return 'focus'
      } else if (sceneId.includes('entertainment') || sceneId.includes('waiting')) {
        return 'entertainment'
      } else if (sceneId.includes('minimal') || sceneId.includes('rainy')) {
        return 'minimal'
      }
      
      return 'default'
    })
    
    // 当前主题
    const currentTheme = computed(() => {
      return props.currentScene?.theme || 'light'
    })
    
    // 获取主要组件
    const primaryComponent = computed(() => {
      const components = sceneComponents[computedLayout.value]
      return components?.primary
    })
    
    // 获取次要组件
    const secondaryComponent = computed(() => {
      const components = sceneComponents[computedLayout.value]
      return components?.secondary
    })
    
    // 获取辅助组件
    const auxiliaryComponent = computed(() => {
      const components = sceneComponents[computedLayout.value]
      return components?.auxiliary
    })
    
    // 获取区域样式
    const getZoneStyle = (zoneType) => {
      const zoneStyles = {
        dynamic: {
          gridArea: 'dynamic',
          minHeight: '0',
          overflow: 'hidden'
        },
        primary: {
          gridArea: 'primary',
          minHeight: '0',
          overflow: 'hidden'
        },
        secondary: {
          gridArea: 'secondary',
          minHeight: '0',
          overflow: 'hidden'
        },
        auxiliary: {
          gridArea: 'auxiliary',
          minHeight: '0',
          overflow: 'hidden'
        },
        vpa: {
          gridArea: 'vpa',
          minHeight: '0',
          overflow: 'hidden'
        }
      }
      
      return zoneStyles[zoneType] || {}
    }
    
    // 获取布局图标
    const getLayoutIcon = computed(() => {
      const iconMap = {
        family: 'fas fa-child',
        focus: 'fas fa-briefcase',
        entertainment: 'fas fa-couch',
        minimal: 'fas fa-moon',
        default: 'fas fa-home'
      }
      return iconMap[computedLayout.value] || 'fas fa-th-large'
    })
    
    // 获取布局名称
    const getLayoutName = computed(() => {
      const nameMap = {
        family: '家庭出行模式',
        focus: '专注通勤模式',
        entertainment: '娱乐等待模式',
        minimal: '极简雨夜模式',
        default: '默认模式'
      }
      return nameMap[computedLayout.value] || '默认模式'
    })
    
    // 处理区域点击
    const handleZoneClick = (zoneType) => {
      emit('zone-click', {
        zone: zoneType,
        layout: computedLayout.value,
        scene: props.currentScene,
        timestamp: Date.now()
      })
    }
    
    // 处理VPA点击
    const handleVpaClick = (data) => {
      emit('vpa-click', {
        ...data,
        layout: computedLayout.value,
        scene: props.currentScene
      })
    }
    
    // 处理VPA模式变更
    const handleVpaModeChanged = (mode) => {
      console.log('VPA模式变更:', mode, '布局:', computedLayout.value)
    }
    
    // 处理VPA动画变更
    const handleVpaAnimationChanged = (animationData) => {
      console.log('VPA动画状态变更:', animationData, '布局:', computedLayout.value)
    }
    
    // 监听布局变更
    const watchLayoutChanges = () => {
      const newLayout = computedLayout.value
      if (newLayout !== currentLayout.value) {
        currentLayout.value = newLayout
        emit('layout-changed', {
          from: currentLayout.value,
          to: newLayout,
          scene: props.currentScene
        })
      }
    }
    
    // 响应式处理
    const handleResize = () => {
      layoutStore.updateScreenSize(window.innerWidth)
    }
    
    onMounted(() => {
      // 初始化屏幕尺寸
      handleResize()
      
      // 监听窗口大小变化
      window.addEventListener('resize', handleResize)
      
      // 监听布局变化
      watchLayoutChanges()
    })
    
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)
    })
    
    return {
      currentLayout,
      currentTheme,
      computedLayout,
      primaryComponent,
      secondaryComponent,
      auxiliaryComponent,
      getZoneStyle,
      getLayoutIcon,
      getLayoutName,
      handleZoneClick,
      handleVpaClick,
      handleVpaModeChanged,
      handleVpaAnimationChanged
    }
  }
}
</script>

<style scoped>
.unified-layout-container {
  width: 100%;
  height: 100vh;
  position: relative;
  background: transparent;
  color: var(--text-color, #ffffff);
  transition: all 0.3s ease;
}

/* 16x9网格系统 */
.grid-system {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: repeat(16, 1fr);
  grid-template-rows: repeat(9, 1fr);
  gap: min(1vw, 10px);
  padding: min(1vw, 10px);
  box-sizing: border-box;
  
  /* 定义网格区域 */
  grid-template-areas:
    "dynamic dynamic dynamic dynamic dynamic dynamic dynamic dynamic dynamic dynamic dynamic dynamic dynamic dynamic dynamic"
    "primary primary primary primary primary primary primary primary secondary secondary secondary secondary secondary secondary vpa"
    "primary primary primary primary primary primary primary primary secondary secondary secondary secondary secondary secondary vpa"
    "primary primary primary primary primary primary primary primary secondary secondary secondary secondary secondary secondary vpa"
    "primary primary primary primary primary primary primary primary secondary secondary secondary secondary secondary secondary vpa"
    "primary primary primary primary primary primary primary primary secondary secondary secondary secondary secondary secondary vpa"
    "primary primary primary primary primary primary primary primary secondary secondary secondary secondary secondary secondary vpa"
    "primary primary primary primary primary primary primary primary auxiliary auxiliary auxiliary auxiliary auxiliary vpa"
    "primary primary primary primary primary primary primary primary auxiliary auxiliary auxiliary auxiliary auxiliary vpa";
}

/* 动态区域 - 灵动岛 */
.dynamic-zone {
  grid-area: dynamic;
}

/* 主要功能区 */
.primary-zone {
  grid-area: primary;
}

/* 次要功能区 */
.secondary-zone {
  grid-area: secondary;
}

/* 辅助功能区 */
.auxiliary-zone {
  grid-area: auxiliary;
}

/* VPA交互区 */
.vpa-zone {
  grid-area: vpa;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  padding: 10px;
}

/* 布局指示器 */
.layout-indicator {
  position: fixed;
  top: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 10px 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1000;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.indicator-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.indicator-content i {
  font-size: 16px;
  color: var(--primary-color, #4a90e2);
}

/* 主题样式 */
.unified-layout-container.light {
  --primary-color: #4a90e2;
  --secondary-color: #7ed321;
  --background: rgba(255, 255, 255, 0.1);
  --text-color: #333333;
}

.unified-layout-container.dark {
  --primary-color: #2c3e50;
  --secondary-color: #3498db;
  --background: rgba(0, 0, 0, 0.3);
  --text-color: #ffffff;
}

.unified-layout-container.warm {
  --primary-color: #e74c3c;
  --secondary-color: #f39c12;
  --background: rgba(255, 193, 7, 0.1);
  --text-color: #2c3e50;
}

.unified-layout-container.calm {
  --primary-color: #3498db;
  --secondary-color: #2ecc71;
  --background: rgba(52, 152, 219, 0.1);
  --text-color: #2c3e50;
}

.unified-layout-container.evening {
  --primary-color: #8e44ad;
  --secondary-color: #e67e22;
  --background: rgba(142, 68, 173, 0.1);
  --text-color: #ffffff;
}

.unified-layout-container.relax {
  --primary-color: #27ae60;
  --secondary-color: #2ecc71;
  --background: rgba(39, 174, 96, 0.1);
  --text-color: #ffffff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-system {
    grid-template-columns: repeat(8, 1fr);
    grid-template-rows: repeat(12, 1fr);
    gap: 5px;
    padding: 5px;
    
    /* 移动端网格布局 */
    grid-template-areas:
      "dynamic dynamic dynamic dynamic dynamic dynamic dynamic dynamic"
      "primary primary primary primary primary primary primary primary"
      "primary primary primary primary primary primary primary primary"
      "primary primary primary primary primary primary primary primary"
      "primary primary primary primary primary primary primary primary"
      "secondary secondary secondary secondary secondary secondary secondary secondary"
      "secondary secondary secondary secondary secondary secondary secondary secondary"
      "auxiliary auxiliary auxiliary auxiliary auxiliary auxiliary auxiliary auxiliary"
      "auxiliary auxiliary auxiliary auxiliary auxiliary auxiliary auxiliary auxiliary"
      "vpa vpa vpa vpa vpa vpa vpa vpa"
      "vpa vpa vpa vpa vpa vpa vpa vpa"
      "vpa vpa vpa vpa vpa vpa vpa vpa";
  }
  
  .layout-indicator {
    top: 10px;
    left: 10px;
    padding: 8px 12px;
  }
  
  .indicator-content {
    font-size: 12px;
  }
  
  .indicator-content i {
    font-size: 14px;
  }
}

/* 动画效果 */
.unified-layout-container * {
  transition: all 0.3s ease;
}

/* 区域悬停效果 */
.primary-zone:hover,
.secondary-zone:hover,
.auxiliary-zone:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 布局切换动画 */
.layout-indicator {
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
</style>