<template>
  <div class="unified-layout-container" :class="[`layout-${layoutMode}`, `theme-${currentTheme}`]">
    <!-- 使用标准16x9网格系统 -->
    <GridSystem16x9 
      :layout-mode="layoutMode"
      :debug-mode="debugMode"
      :responsive="responsive"
      :animated="animated"
      @layout-changed="handleLayoutChanged"
      @grid-ready="handleGridReady"
    >
      <!-- 动态渲染场景组件 -->
      <component
        v-for="component in sceneComponents"
        :key="component.id"
        :is="component.componentName"
        v-bind="component.props"
        :style="getComponentGridStyle(component)"
        @click="handleComponentClick(component)"
        @zone-click="handleZoneClick"
        @vpa-click="handleVpaClick"
      />
      
      <!-- 灵动岛 - 固定在顶部 -->
      <DynamicIsland
        v-if="showDynamicIsland"
        :style="getDynamicIslandStyle()"
        :priority="dynamicIslandPriority"
        @island-click="handleIslandClick"
      />
    </GridSystem16x9>
  </div>
</template>

<script>
import { computed, ref, watch } from 'vue'
import { useLayoutStore } from '@/store'
import GridSystem16x9 from './layout/GridSystem16x9.vue'
import DynamicIsland from './DynamicIsland.vue'

// 导入所有卡片组件
import DefaultCard from './cards/DefaultCard.vue'
import KidEducationCard from './cards/KidEducationCard.vue'
import MusicControlCard from './cards/MusicControlCard.vue'
import AIScheduleAssistantCard from './cards/AIScheduleAssistantCard.vue'
import VPAAvatarWidget from './vpa/VPAAvatarWidget.vue'
import VPAInteractionPanel from './vpa/VPAInteractionPanel.vue'

export default {
  name: 'UnifiedLayoutContainer',
  components: {
    GridSystem16x9,
    DynamicIsland,
    DefaultCard,
    KidEducationCard,
    MusicControlCard,
    AIScheduleAssistantCard,
    VPAAvatarWidget,
    VPAInteractionPanel
  },
  props: {
    // 当前场景信息
    currentScene: {
      type: Object,
      required: true
    },
    
    // 主题颜色
    themeColors: {
      type: Object,
      default: () => ({})
    },
    
    // 布局模式
    layoutMode: {
      type: String,
      default: 'default'
    },
    
    // 当前主题
    currentTheme: {
      type: String,
      default: 'glass'
    },
    
    // 调试模式
    debugMode: {
      type: Boolean,
      default: false
    },
    
    // 响应式
    responsive: {
      type: Boolean,
      default: true
    },
    
    // 动画
    animated: {
      type: Boolean,
      default: true
    },
    
    // 显示灵动岛
    showDynamicIsland: {
      type: Boolean,
      default: true
    }
  },
  
  emits: ['zone-click', 'vpa-click', 'layout-changed', 'component-click', 'island-click'],
  
  setup(props, { emit }) {
    const layoutStore = useLayoutStore()
    
    // 场景组件配置映射
    const sceneComponentConfigs = {
      // 家庭出行场景
      family: [
        {
          id: 'kidEducation',
          componentName: 'KidEducationCard',
          componentType: 'kidEducation',
          position: { x: 1, y: 2 },
          props: {
            theme: 'glass',
            themeColors: props.themeColors
          }
        },
        {
          id: 'pedia',
          componentName: 'DefaultCard',
          componentType: 'pedia',
          position: { x: 9, y: 2 },
          props: {
            cardType: 'pedia',
            theme: 'glass',
            themeColors: props.themeColors
          }
        },
        {
          id: 'vpaWidget',
          componentName: 'VPAAvatarWidget',
          componentType: 'vpaWidget',
          position: { x: 15, y: 7 },
          props: {
            size: 'small',
            theme: 'glass',
            themeColors: props.themeColors
          }
        }
      ],
      
      // 专注通勤场景
      focus: [
        {
          id: 'music',
          componentName: 'MusicControlCard',
          componentType: 'music',
          position: { x: 1, y: 2 },
          props: {
            theme: 'glass',
            themeColors: props.themeColors
          }
        },
        {
          id: 'schedule',
          componentName: 'AIScheduleAssistantCard',
          componentType: 'todo',
          position: { x: 9, y: 2 },
          props: {
            theme: 'glass',
            themeColors: props.themeColors
          }
        },
        {
          id: 'orderStatus',
          componentName: 'DefaultCard',
          componentType: 'orderStatus',
          position: { x: 9, y: 5 },
          props: {
            cardType: 'orderStatus',
            theme: 'glass',
            themeColors: props.themeColors
          }
        },
        {
          id: 'vpaWidget',
          componentName: 'VPAAvatarWidget',
          componentType: 'vpaWidget',
          position: { x: 15, y: 7 },
          props: {
            size: 'small',
            theme: 'glass',
            themeColors: props.themeColors
          }
        }
      ],
      
      // 默认场景
      default: [
        {
          id: 'defaultCard',
          componentName: 'DefaultCard',
          componentType: 'cardMedium',
          position: { x: 6, y: 4 },
          props: {
            cardType: 'default',
            title: '欢迎使用AI-HMI',
            description: '智能车载人机交互系统',
            theme: 'glass',
            themeColors: props.themeColors
          }
        }
      ]
    }
    
    // 计算当前场景的组件列表
    const sceneComponents = computed(() => {
      const sceneLayout = props.currentScene?.layout || 'default'
      const components = sceneComponentConfigs[sceneLayout] || sceneComponentConfigs.default
      
      // 过滤只显示场景中包含的卡片
      return components.filter(component => {
        if (!props.currentScene?.cards) return true
        return props.currentScene.cards.includes(component.id)
      })
    })
    
    // 灵动岛优先级
    const dynamicIslandPriority = ref(['navigation', 'music', 'calls', 'notifications'])
    
    // 获取组件网格样式
    const getComponentGridStyle = (component) => {
      return layoutStore.getComponentPosition(component.componentType, component.position)
    }
    
    // 获取灵动岛样式
    const getDynamicIslandStyle = () => {
      return layoutStore.getComponentPosition('dynamicIsland', { x: 1, y: 1 })
    }
    
    // 事件处理
    const handleLayoutChanged = (layoutData) => {
      emit('layout-changed', layoutData)
    }
    
    const handleGridReady = (gridConfig) => {
      console.log('网格系统就绪:', gridConfig)
    }
    
    const handleComponentClick = (component) => {
      emit('component-click', component)
    }
    
    const handleZoneClick = (zone) => {
      emit('zone-click', zone)
    }
    
    const handleVpaClick = (vpaData) => {
      emit('vpa-click', vpaData)
    }
    
    const handleIslandClick = (islandData) => {
      emit('island-click', islandData)
    }
    
    return {
      sceneComponents,
      dynamicIslandPriority,
      getComponentGridStyle,
      getDynamicIslandStyle,
      handleLayoutChanged,
      handleGridReady,
      handleComponentClick,
      handleZoneClick,
      handleVpaClick,
      handleIslandClick
    }
  }
}
</script>

<style scoped>
.unified-layout-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

/* 主题样式 */
.theme-glass {
  /* 玻璃主题的特定样式 */
}

.theme-solid {
  /* 实体主题的特定样式 */
}

.theme-minimal {
  /* 极简主题的特定样式 */
}

/* 布局模式样式 */
.layout-family {
  /* 家庭模式的特定样式 */
}

.layout-focus {
  /* 专注模式的特定样式 */
}

.layout-default {
  /* 默认模式的特定样式 */
}
</style>
