<template>
  <div class="scene-manager" :style="sceneStyles">
    <!-- 场景指示器 -->
    <div class="scene-indicator" v-if="showIndicator">
      <div class="scene-info">
        <i :class="getSceneIcon"></i>
        <span class="scene-name">{{ currentScene.name }}</span>
        <span class="scene-description">{{ currentScene.description }}</span>
      </div>
      <div class="scene-controls">
        <button @click="toggleSceneSelector" class="control-btn">
          <i class="fas fa-th-large"></i>
        </button>
        <button @click="toggleAutoSwitch" class="control-btn" :class="{ active: autoSwitchEnabled }">
          <i class="fas fa-magic"></i>
        </button>
      </div>
    </div>

    <!-- 场景选择器 -->
    <div v-if="showSceneSelector" class="scene-selector">
      <div class="selector-header">
        <h3>选择场景</h3>
        <button @click="closeSceneSelector" class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="scene-grid">
        <div 
          v-for="scene in availableScenes" 
          :key="scene.id"
          :class="['scene-card', { active: currentScene.id === scene.id }]"
          @click="selectScene(scene.id)"
        >
          <div class="scene-card-header">
            <i :class="getSceneIconByType(scene.id)"></i>
            <h4>{{ scene.name }}</h4>
          </div>
          <p class="scene-description">{{ scene.description }}</p>
          <div class="scene-meta">
            <span class="scene-priority" :style="{ backgroundColor: getPriorityColor(scene.priority) }">
              优先级 {{ scene.priority }}
            </span>
            <span class="scene-auto-switch" v-if="scene.autoSwitch">
              <i class="fas fa-magic"></i> 自动
            </span>
          </div>
        </div>
      </div>

      <!-- 推荐场景 -->
      <div v-if="recommendedScenes.length > 0" class="recommended-scenes">
        <h4>推荐场景</h4>
        <div class="recommendation-list">
          <div 
            v-for="recommendation in recommendedScenes" 
            :key="recommendation.sceneId"
            class="recommendation-item"
            @click="selectScene(recommendation.sceneId)"
          >
            <div class="recommendation-info">
              <i :class="getSceneIconByType(recommendation.sceneId)"></i>
              <div>
                <div class="recommendation-name">{{ recommendation.scene.name }}</div>
                <div class="recommendation-score">匹配度: {{ recommendation.score }}</div>
              </div>
            </div>
            <button class="apply-btn">
              <i class="fas fa-check"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 场景内容区域 -->
    <div class="scene-content" :class="currentScene.layout">
      <slot :scene="currentScene" :layout="currentScene.layout">
        <!-- 使用统一布局容器 -->
        <UnifiedLayoutContainer
          :current-scene="currentScene"
          :theme-colors="themeColors"
          :layout-mode="currentScene.layout"
          :current-theme="currentScene.theme"
          :debug-mode="false"
          :responsive="true"
          :animated="true"
          :show-dynamic-island="true"
          @zone-click="handleZoneClick"
          @vpa-click="handleVpaClick"
          @layout-changed="handleLayoutChanged"
          @component-click="handleComponentClick"
          @island-click="handleIslandClick"
        />
            <!-- 灵动岛 16x1 -->
            <div class="dynamic-island">
              <DefaultCard
                :scene="currentScene"
                card-type="dynamicIsland"
                :theme-colors="themeColors"
              />
            </div>

            <!-- 儿童教育卡片 8x8 -->
            <div class="kid-education-card">
              <KidEducationCard
                v-if="currentScene.cards.includes('kidEducation')"
                :position="{ x: 1, y: 2 }"
                :theme="'glass'"
                :theme-colors="themeColors"
                @card-click="handleCardClick"
                @mode-changed="handleEducationModeChanged"
                @lesson-completed="handleLessonCompleted"
              />
            </div>

            <!-- 百科问答卡片 8x3 -->
            <div class="pedia-card">
              <DefaultCard
                v-if="currentScene.cards.includes('pedia')"
                :scene="currentScene"
                card-type="pedia"
              />
            </div>

            <!-- VPA小窗 2x2 -->
            <div class="vpa-widget">
              <VPAAvatarWidget
                :size="'small'"
                :position="{ x: 15, y: 7 }"
                :theme="'glass'"
                :theme-colors="themeColors"
                @avatar-click="handleVpaClick"
                @mode-changed="handleVpaModeChanged"
                @animation-changed="handleVpaAnimationChanged"
              />
            </div>
          </div>

          <div v-else-if="currentScene.layout === 'focus'" class="focus-layout">
            <!-- 灵动岛 16x1 -->
            <div class="dynamic-island">
              <DefaultCard
                :scene="currentScene"
                card-type="dynamicIsland"
              />
            </div>

            <!-- 音乐控制卡片 8x8 -->
            <div class="music-card">
              <MusicControlCard
                v-if="currentScene.cards.includes('music')"
                :position="{ x: 1, y: 2 }"
                :theme="'glass'"
                :theme-colors="themeColors"
                @card-click="handleCardClick"
                @song-changed="handleSongChanged"
                @play-state-changed="handlePlayStateChanged"
              />
            </div>

            <!-- 今日待办卡片 8x3 -->
            <div class="todo-card">
              <DefaultCard
                v-if="currentScene.cards.includes('todo')"
                :scene="currentScene"
                card-type="todo"
              />
            </div>

            <!-- 订单状态卡片 4x2 -->
            <div class="order-card">
              <DefaultCard
                v-if="currentScene.cards.includes('orderStatus')"
                :scene="currentScene"
                card-type="orderStatus"
              />
            </div>

            <!-- VPA小窗 2x2 -->
            <div class="vpa-widget">
              <DefaultCard
                :scene="currentScene"
                card-type="vpaWidget"
              />
            </div>
          </div>

          <!-- 沉浸式桌面壁纸界面 -->
          <div v-else-if="currentScene.layout === 'immersive'" class="immersive-layout">
            <ImmersiveWallpaperInterface @wallpaper-prompt-ready="handleWallpaperPrompt" />
          </div>

          <div v-else-if="currentScene.layout === 'entertainment'" class="entertainment-layout">
            <!-- 等待/娱乐模式布局 -->
            <div class="layout-row video-row">
              <div class="card-video">
                <DefaultCard
                  v-if="currentScene.cards.includes('videoPlayer')"
                  :scene="currentScene"
                  card-type="videoPlayer"
                  :theme-colors="themeColors"
                />
              </div>
            </div>

            <!-- VPA小窗 -->
            <div class="vpa-widget">
              <DefaultCard
                v-if="currentScene.cards.includes('vpaWidget')"
                :scene="currentScene"
                card-type="vpaWidget"
                :theme-colors="themeColors"
              />
            </div>

            <div class="layout-row bottom-row">
              <div class="card-small">
                <DefaultCard
                  v-if="currentScene.cards.includes('news')"
                  :scene="currentScene"
                  card-type="news"
                  :theme-colors="themeColors"
                />
              </div>
              <div class="card-small">
                <DefaultCard
                  v-if="currentScene.cards.includes('ambientSound')"
                  :scene="currentScene"
                  card-type="ambientSound"
                  :theme-colors="themeColors"
                />
              </div>
            </div>
          </div>

          <div v-else-if="currentScene.layout === 'minimal'" class="minimal-layout">
            <!-- 雨夜模式极简布局 -->
            <!-- 灵动岛 -->
            <div class="dynamic-island-minimal">
              <DefaultCard
                v-if="currentScene.cards.includes('navigation')"
                :scene="currentScene"
                card-type="dynamicIsland"
                :theme-colors="themeColors"
              />
            </div>

            <!-- 底部组件区域 -->
            <div class="bottom-components">
              <!-- VPA组件 (左下角) -->
              <div class="vpa-minimal">
                <DefaultCard
                  v-if="currentScene.cards.includes('vpaWidget')"
                  :scene="currentScene"
                  card-type="vpaWidget"
                  :theme-colors="themeColors"
                />
              </div>

              <!-- 音乐控制 (右下角) -->
              <div class="card-minimal">
                <DefaultCard
                  v-if="currentScene.cards.includes('music')"
                  :scene="currentScene"
                  card-type="music"
                  :theme-colors="themeColors"
                />
              </div>
            </div>
          </div>

          <div v-else class="default-layout">
            <!-- 默认网格布局 -->
            <div class="layout-grid">
              <div v-for="(card, index) in currentScene.cards" :key="index" class="scene-card-slot">
                <DefaultCard :scene="currentScene" :card-type="card" :theme-colors="themeColors" />
              </div>
            </div>
          </div>
        </div>
      </slot>
    </div>

    <!-- 场景切换动画 -->
    <transition name="scene-transition">
      <div v-if="isTransitioning" class="scene-transition-overlay">
        <div class="transition-content">
          <i class="fas fa-spinner fa-spin"></i>
          <span>正在切换场景...</span>
        </div>
      </div>
    </transition>

    <!-- 语音交互管理器 -->
    <VoiceInteractionManager 
      @scene-switch-requested="handleVoiceSceneSwitch"
      @wallpaper-prompt-ready="handleWallpaperPrompt"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import SceneManager from '@/utils/SceneManager'
import EmotionalPromptGenerator from '@/services/EmotionalPromptGenerator'
import SceneContextManager from '@/services/SceneContextManager'

import DefaultCard from './cards/DefaultCard.vue'
import KidEducationCard from './cards/KidEducationCard.vue'
import MusicControlCard from './cards/MusicControlCard.vue'
import VPAAvatarWidget from './vpa/VPAAvatarWidget.vue'
import ImmersiveWallpaperInterface from './ImmersiveWallpaperInterface.vue'
import VoiceInteractionManager from './VoiceInteractionManager.vue'

export default {
  name: 'SceneManager',
  components: {
    DefaultCard,
    KidEducationCard,
    MusicControlCard,
    VPAAvatarWidget,
    ImmersiveWallpaperInterface,
    VoiceInteractionManager
  },
  props: {
    initialScene: {
      type: String,
      default: 'default'
    },
    showIndicator: {
      type: Boolean,
      default: true
    },
    autoSwitch: {
      type: Boolean,
      default: false  // 默认禁用自动切换，避免意外的场景切换
    },
    themeColors: {
      type: Object,
      default: null
    }
  },
  emits: ['scene-changed', 'context-updated', 'wallpaper-prompt-ready'],
  setup(props, { emit }) {
    const sceneManager = new SceneManager()
    const emotionalPromptGenerator = new EmotionalPromptGenerator()
    const contextManager = new SceneContextManager()
    
    const showSceneSelector = ref(false)
    const autoSwitchEnabled = ref(props.autoSwitch)
    const isTransitioning = ref(false)
    
    // 当前场景
    const currentScene = ref(sceneManager.getCurrentScene())
    
    // 可用场景
    const availableScenes = computed(() => sceneManager.getAllScenes())
    
    // 推荐场景
    const recommendedScenes = ref([])
    
    // 场景样式
    const sceneStyles = computed(() => {
      const scene = currentScene.value

      // 移除背景样式，让DynamicWallpaperManager管理背景
      let backgroundStyle = {}

      return {
        '--scene-primary-color': getThemeColor(scene.theme, 'primary'),
        '--scene-secondary-color': getThemeColor(scene.theme, 'secondary'),
        '--scene-background': 'transparent', /* 使用透明背景，让动态壁纸显示 */
        '--scene-text-color': getThemeColor(scene.theme, 'text'),
        ...backgroundStyle
      }
    })
    
    // 获取场景图标
    const getSceneIcon = computed(() => {
      return getSceneIconByType(currentScene.value.id)
    })
    
    // 选择场景
    const selectScene = async (sceneId) => {
      if (sceneId === currentScene.value.id) return

      isTransitioning.value = true

      try {
        // 触发场景切换
        const success = sceneManager.switchScene(sceneId, 'manual')

        if (success) {
          // 更新当前场景的响应式状态
          currentScene.value = sceneManager.getCurrentScene()

          // 更新上下文管理器
          contextManager.updateContext({
            recentScenes: [...contextManager.context.recentScenes, sceneId].slice(-5),
            sceneSwitchCount: contextManager.context.sceneSwitchCount + 1
          })

          // 生成场景对应的壁纸
          await generateSceneWallpaper(currentScene.value)

          // 发送事件
          emit('scene-changed', {
            from: sceneManager.sceneHistory[0]?.from,
            to: sceneId,
            scene: currentScene.value,
            context: contextManager.getContext()
          })

          // 关闭选择器
          showSceneSelector.value = false
        }
      } catch (error) {
        console.error('场景切换失败:', error)
      } finally {
        setTimeout(() => {
          isTransitioning.value = false
        }, 1000)
      }
    }
    
    // 生成场景壁纸
    const generateSceneWallpaper = async (scene) => {
      if (!scene.wallpaper || scene.wallpaper.startsWith('/')) {
        // 使用默认壁纸
        return scene.wallpaper
      }
      
      try {
        console.log('🎨 开始生成情感化壁纸提示词...')
        
        // 获取当前上下文信息
        const promptContext = contextManager.getPromptGenerationContext()
        console.log('📋 当前上下文:', promptContext)
        
        // 生成情感化提示词
        const emotionalPrompt = await emotionalPromptGenerator.generateEmotionalPrompt(
          scene,
          promptContext
        )
        
        console.log('🎭 情感化提示词生成成功:', emotionalPrompt)
        
        // 触发壁纸生成事件
        emit('wallpaper-prompt-ready', {
          prompt: emotionalPrompt,
          scene: scene,
          context: promptContext
        })
        
        return emotionalPrompt
        
      } catch (error) {
        console.error('情感化壁纸生成失败:', error)
        
        // 使用增强的场景描述作为降级方案
        const fallbackPrompt = emotionalPromptGenerator.getFallbackPrompt(
          scene,
          contextManager.getPromptGenerationContext()
        )
        
        console.log('🔄 使用降级提示词:', fallbackPrompt)
        
        // 触发壁纸生成事件
        emit('wallpaper-prompt-ready', {
          prompt: fallbackPrompt,
          scene: scene,
          context: contextManager.getPromptGenerationContext()
        })
        
        return fallbackPrompt
      }
    }
    
    // 切换场景选择器
    const toggleSceneSelector = () => {
      showSceneSelector.value = !showSceneSelector.value
      if (showSceneSelector.value) {
        updateRecommendations()
      }
    }
    
    // 关闭场景选择器
    const closeSceneSelector = () => {
      showSceneSelector.value = false
    }
    
    // 切换自动切换
    const toggleAutoSwitch = () => {
      autoSwitchEnabled.value = !autoSwitchEnabled.value
    }
    
    // 更新推荐场景
    const updateRecommendations = () => {
      const context = getCurrentContext()
      recommendedScenes.value = sceneManager.getRecommendedScenes(context)
    }
    
    // 获取当前上下文
    const getCurrentContext = () => {
      const now = new Date()
      return {
        time: now.getHours(),
        day: now.getDay(),
        isWeekend: now.getDay() === 0 || now.getDay() === 6,
        passengers: ['driver'], // 可以通过传感器获取
        passengerCount: 1,
        destination: '', // 可以通过导航获取
        gear: 'D', // 可以通过车辆系统获取
        drivingDuration: 0, // 可以通过车辆系统获取
        weather: 'clear', // 可以通过天气API获取
        roadType: 'city', // 可以通过GPS获取
        batteryLevel: 85, // 可以通过车辆系统获取
        locationType: 'city', // 可以通过GPS获取
        fatigueDetected: false, // 可以通过疲劳检测系统获取
        manualTrigger: null,
        calendarEvents: [], // 可以通过日历API获取
        newUserDetected: false,
        accidentDetected: false,
        airbagDeployed: false
      }
    }
    
    // 模拟上下文更新
    const simulateContextUpdate = () => {
      contextManager.simulateDrivingUpdate()
    }
    
    // 获取卡片组件
    const getCardComponent = (card) => {
      // 根据卡片类型返回对应的组件名称
      const cardComponents = {
        // 导航相关
        'navigation': 'NavigationCard',
        'tempNavigation': 'NavigationCard',

        // 音乐相关
        'music': 'MusicCard',
        'basicMusic': 'MusicCard',
        'romanticMusic': 'MusicCard',

        // 待办事项
        'todo': 'TodoCard',

        // 儿童教育
        'kidEducation': 'KidEducationCard',
        'pedia': 'PediaCard',

        // 娱乐相关
        'videoPlayer': 'VideoPlayerCard',
        'news': 'NewsCard',
        'ambientSound': 'AmbientSoundCard',

        // 智能家居
        'smartHome': 'SmartHomeCard',

        // 家庭出行
        'rearSeatControl': 'RearSeatControlCard',
        'facilityFinder': 'FacilityFinderCard',
        'tripReminder': 'TripReminderCard',

        // 长途驾驶
        'serviceArea': 'ServiceAreaCard',
        'driverStatus': 'DriverStatusCard',
        'vehicleStatus': 'VehicleStatusCard',

        // 充电相关
        'chargingStatus': 'ChargingStatusCard',
        'entertainment': 'EntertainmentCard',
        'nearbyShops': 'NearbyShopsCard',

        // 其他功能
        'orderStatus': 'OrderStatusCard',
        'basicControl': 'BasicControlCard',
        'petInfo': 'PetInfoCard',
        'climateControl': 'ClimateControlCard',
        'carWashChecklist': 'CarWashChecklistCard',
        'ambientLight': 'AmbientLightCard',

        // 安全相关
        'fatigueWarning': 'FatigueWarningCard',
        'restArea': 'RestAreaCard',
        'refreshment': 'RefreshmentCard',
        'emergencyContact': 'EmergencyContactCard',
        'emergencyInfo': 'EmergencyInfoCard',
        'firstAid': 'FirstAidCard',

        // 用户管理
        'userSelector': 'UserSelectorCard',
        'userPreferences': 'UserPreferencesCard',
        'privacySettings': 'PrivacySettingsCard',

        // 泊车相关
        'parkingSearch': 'ParkingSearchCard',
        'parkingAssist': 'ParkingAssistCard',
        'costInfo': 'CostInfoCard'
      }

      return cardComponents[card] || 'DefaultCard'
    }

    // 获取场景图标
    const getSceneIconByType = (sceneId) => {
      const iconMap = {
        default: 'fas fa-home',
        morningCommuteFamily: 'fas fa-child',
        morningCommuteFocus: 'fas fa-briefcase',
        eveningCommute: 'fas fa-sun',
        waitingMode: 'fas fa-couch',
        rainyNight: 'fas fa-cloud-rain',
        familyTrip: 'fas fa-car',
        longDistance: 'fas fa-road',
        guestMode: 'fas fa-user-shield',
        petMode: 'fas fa-paw',
        carWashMode: 'fas fa-car-side',
        romanticMode: 'fas fa-heart',
        chargingMode: 'fas fa-charging-station',
        fatigueDetection: 'fas fa-exclamation-triangle',
        userSwitch: 'fas fa-users',
        parkingMode: 'fas fa-parking',
        emergencyMode: 'fas fa-ambulance'
      }
      return iconMap[sceneId] || 'fas fa-circle'
    }

    // 获取优先级颜色
    const getPriorityColor = (priority) => {
      const colorMap = {
        1: '#e74c3c', // 红色 - 最高优先级
        2: '#f39c12', // 橙色 - 高优先级
        3: '#f1c40f', // 黄色 - 中优先级
        4: '#2ecc71', // 绿色 - 低优先级
        5: '#95a5a6'  // 灰色 - 最低优先级
      }
      return colorMap[priority] || '#95a5a6'
    }

    // 获取主题颜色
    const getThemeColor = (theme, type) => {
      const themeColors = {
        light: {
          primary: '#4a90e2',
          secondary: '#7ed321',
          background: 'rgba(255, 255, 255, 0.1)',
          text: '#333333'
        },
        dark: {
          primary: '#2c3e50',
          secondary: '#3498db',
          background: 'rgba(0, 0, 0, 0.3)',
          text: '#ffffff'
        },
        warm: {
          primary: '#e74c3c',
          secondary: '#f39c12',
          background: 'rgba(255, 193, 7, 0.1)',
          text: '#2c3e50'
        },
        calm: {
          primary: '#3498db',
          secondary: '#2ecc71',
          background: 'rgba(52, 152, 219, 0.1)',
          text: '#2c3e50'
        },
        evening: {
          primary: '#8e44ad',
          secondary: '#e67e22',
          background: 'rgba(142, 68, 173, 0.1)',
          text: '#ffffff'
        },
        relax: {
          primary: '#27ae60',
          secondary: '#2ecc71',
          background: 'rgba(39, 174, 96, 0.1)',
          text: '#ffffff'
        },
        bright: {
          primary: '#f39c12',
          secondary: '#e67e22',
          background: 'rgba(243, 156, 18, 0.1)',
          text: '#2c3e50'
        }
      }
      return themeColors[theme]?.[type] || themeColors.light[type]
    }
    
    // 监听自动切换
    let autoSwitchInterval
    const startAutoSwitch = () => {
      if (!autoSwitchEnabled.value) return
      
      autoSwitchInterval = setInterval(() => {
        updateRecommendations()
        
        // 如果有高优先级的推荐场景，自动切换
        const topRecommendation = recommendedScenes.value[0]
        if (topRecommendation && topRecommendation.score > 8) {
          selectScene(topRecommendation.sceneId)
        }
      }, 30000) // 每30秒检查一次
    }
    
    const stopAutoSwitch = () => {
      if (autoSwitchInterval) {
        clearInterval(autoSwitchInterval)
        autoSwitchInterval = null
      }
    }
    
    // 监听自动切换状态
    watch(autoSwitchEnabled, (newValue) => {
      if (newValue) {
        startAutoSwitch()
      } else {
        stopAutoSwitch()
      }
    })
    
    // 初始化
    onMounted(() => {
      // 设置初始场景
      if (props.initialScene !== 'default') {
        sceneManager.switchScene(props.initialScene, 'initial')
      }
      
      // 初始化上下文
      contextManager.updateContext({
        isDriving: true, // 假设车辆在行驶中
        recentScenes: [props.initialScene === 'default' ? 'default' : props.initialScene]
      })
      
      // 启动自动切换
      if (autoSwitchEnabled.value) {
        startAutoSwitch()
      }
      
      // 启动上下文更新定时器
      setInterval(simulateContextUpdate, 60000) // 每分钟更新一次
      
      // 监听键盘事件
      window.addEventListener('keydown', handleKeyDown)
    })
    
    onUnmounted(() => {
      stopAutoSwitch()
      window.removeEventListener('keydown', handleKeyDown)
    })
    
    // 键盘事件处理
    const handleKeyDown = (event) => {
      if (event.key === 'Escape') {
        closeSceneSelector()
      } else if (event.ctrlKey && event.key === 's') {
        event.preventDefault()
        toggleSceneSelector()
      }
    }

    // 处理语音场景切换请求
    const handleVoiceSceneSwitch = async (sceneRequest) => {
      console.log('收到语音场景切换请求:', sceneRequest)
      
      if (sceneRequest.sceneId && sceneRequest.confidence > 0.6) {
        try {
          await selectScene(sceneRequest.sceneId)
          console.log(`语音场景切换成功: ${sceneRequest.sceneId} (置信度: ${sceneRequest.confidence})`)
        } catch (error) {
          console.error('语音场景切换失败:', error)
        }
      } else {
        console.log(`语音场景切换置信度过低: ${sceneRequest.confidence}`)
      }
    }

    // 处理壁纸生成请求
    function handleWallpaperPrompt(prompt) {
      console.log('收到壁纸生成请求:', prompt)
      
      // 如果是直接字符串，包装为对象格式
      const promptData = typeof prompt === 'string' ? {
        prompt,
        scene: currentScene.value,
        context: contextManager.getPromptGenerationContext()
      } : prompt
      
      // 触发壁纸生成事件，传递给父组件
      emit('wallpaper-prompt-ready', promptData)
    }

    // 新组件事件处理方法
    const handleCardClick = (cardType) => {
      console.log('卡片被点击:', cardType)
      // 可以在这里处理卡片点击事件，比如打开详细界面
    }

    const handleEducationModeChanged = (mode) => {
      console.log('教育模式变更:', mode)
      // 可以在这里处理教育模式变更
    }

    const handleLessonCompleted = (lessonType) => {
      console.log('课程完成:', lessonType)
      // 可以在这里处理课程完成事件，比如更新进度
    }

    const handleSongChanged = (song) => {
      console.log('歌曲变更:', song)
      // 可以在这里处理歌曲变更事件
    }

    const handlePlayStateChanged = (state) => {
      console.log('播放状态变更:', state)
      // 可以在这里处理播放状态变更
    }

    const handleVpaClick = (data) => {
      console.log('VPA头像被点击:', data)
      // 可以在这里处理VPA交互
    }

    const handleVpaModeChanged = (mode) => {
      console.log('VPA模式变更:', mode)
      // 可以在这里处理VPA模式变更
    }

    const handleVpaAnimationChanged = (animationData) => {
      console.log('VPA动画状态变更:', animationData)
      // 可以在这里处理VPA动画状态变更
    }

    return {
      currentScene,
      availableScenes,
      recommendedScenes,
      showSceneSelector,
      autoSwitchEnabled,
      isTransitioning,
      sceneStyles,
      getSceneIcon,
      getSceneIconByType,
      getPriorityColor,
      getThemeColor,
      selectScene,
      toggleSceneSelector,
      closeSceneSelector,
      toggleAutoSwitch,
      getCardComponent,
      handleWallpaperPrompt,
      handleVoiceSceneSwitch,
      // 新组件事件处理方法
      handleCardClick,
      handleEducationModeChanged,
      handleLessonCompleted,
      handleSongChanged,
      handlePlayStateChanged,
      handleVpaClick,
      handleVpaModeChanged,
      handleVpaAnimationChanged,
      // 暴露给模板的上下文信息
      contextStats: computed(() => contextManager.getStatistics())
    }
  }
}
</script>

<style scoped>
.scene-manager {
  width: 100%;
  height: 100vh;
  position: relative;
  background: transparent; /* 透明背景，让动态壁纸显示 */
  color: var(--scene-text-color);
  transition: all 0.5s ease;
}

.scene-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 15px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  z-index: 1000;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.scene-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.scene-info i {
  font-size: 18px;
  color: var(--scene-primary-color);
}

.scene-name {
  font-weight: 600;
  font-size: 14px;
}

.scene-description {
  font-size: 12px;
  opacity: 0.8;
  margin-left: 10px;
}

.scene-controls {
  display: flex;
  gap: 10px;
}

.control-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));
  background: var(--button-bg, rgba(74, 144, 226, 0.8));
  color: var(--button-color, white);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  text-shadow: var(--button-text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.control-btn:hover {
  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.control-btn.active {
  background: var(--scene-primary-color);
  color: white;
}

.scene-selector {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 2000;
  display: flex;
  flex-direction: column;
  padding: 20px;
  overflow-y: auto;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 20px;
  border-radius: 15px;
}

.selector-header h3 {
  margin: 0;
  color: white;
  font-size: 24px;
}

.close-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));
  background: var(--button-bg, rgba(74, 144, 226, 0.8));
  color: var(--button-color, white);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  text-shadow: var(--button-text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.close-btn:hover {
  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.scene-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.scene-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.scene-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-5px);
}

.scene-card.active {
  background: var(--scene-primary-color);
  border-color: var(--scene-primary-color);
}

.scene-card-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.scene-card-header i {
  font-size: 20px;
  color: var(--scene-primary-color);
}

.scene-card.active .scene-card-header i {
  color: white;
}

.scene-card-header h4 {
  margin: 0;
  font-size: 16px;
  color: white;
}

.scene-card .scene-description {
  font-size: 14px;
  opacity: 0.8;
  margin-bottom: 15px;
  color: white;
}

.scene-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.scene-priority {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  color: white;
}

.scene-auto-switch {
  font-size: 12px;
  color: var(--scene-primary-color);
  display: flex;
  align-items: center;
  gap: 5px;
}

.scene-card.active .scene-auto-switch {
  color: white;
}

.recommended-scenes {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 20px;
}

.recommended-scenes h4 {
  margin: 0 0 15px 0;
  color: white;
  font-size: 18px;
}

.recommendation-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.recommendation-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.recommendation-item:hover {
  background: rgba(255, 255, 255, 0.15);
}

.recommendation-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.recommendation-info i {
  font-size: 16px;
  color: var(--scene-primary-color);
}

.recommendation-name {
  font-weight: 600;
  color: white;
  font-size: 14px;
}

.recommendation-score {
  font-size: 12px;
  opacity: 0.8;
  color: white;
}

.apply-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 1px solid var(--button-border, rgba(255, 255, 255, 0.6));
  background: var(--button-bg, rgba(74, 144, 226, 0.8));
  color: var(--button-color, white);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  text-shadow: var(--button-text-shadow, 0 1px 2px rgba(0, 0, 0, 0.8));
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.apply-btn:hover {
  background: var(--button-hover-bg, rgba(104, 174, 256, 0.9));
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.scene-content {
  width: 100%;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
}

/* 布局样式 */
.scene-content.standard {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 20px;
}

.scene-content.family {
  display: grid;
  grid-template-columns: 2fr 1fr;
  grid-template-rows: 2fr 1fr;
  gap: 20px;
}

.scene-content.focus {
  display: grid;
  grid-template-columns: 2fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 20px;
}

.scene-content.relax {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.scene-content.entertainment {
  display: grid;
  grid-template-columns: 2fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 20px;
}

.scene-content.minimal {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
}

.scene-content.driving {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.scene-content.basic {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.scene-content.userSelection {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.scene-content.parking {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
}

.scene-content.emergency {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

/* 场景布局容器 */
.scene-layout {
  width: 100%;
  height: 100vh;
  min-height: 600px;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
}

/* 家庭出行模式布局 - 16x9网格 */
.family-layout {
  display: grid;
  grid-template-columns: repeat(16, 1fr);
  grid-template-rows: repeat(9, 1fr);
  gap: min(1vw, 10px);
  width: 100%;
  height: 100%;
  padding: min(1vw, 10px);
}

/* 灵动岛 16x1 */
.family-layout .dynamic-island {
  grid-column: 1 / 17;
  grid-row: 1 / 2;
  min-height: 0;
  overflow: hidden;
}

/* 儿童教育卡片 8x8 (调整为给灵动岛让出空间) */
.family-layout .kid-education-card {
  grid-column: 1 / 9;
  grid-row: 2 / 10;
  min-height: 0;
  overflow: hidden;
}

/* 百科问答卡片 8x3 */
.family-layout .pedia-card {
  grid-column: 9 / 17;
  grid-row: 2 / 5;
  min-height: 0;
  overflow: hidden;
}

/* VPA小窗 2x2 */
.family-layout .vpa-widget {
  grid-column: 15 / 17;
  grid-row: 7 / 9;
  min-height: 0;
  overflow: hidden;
}

/* 专注通勤模式布局 - 16x9网格 */
.focus-layout {
  display: grid;
  grid-template-columns: repeat(16, 1fr);
  grid-template-rows: repeat(9, 1fr);
  gap: min(1vw, 10px);
  width: 100%;
  height: 100%;
  padding: min(1vw, 10px);
}

/* 灵动岛 16x1 */
.focus-layout .dynamic-island {
  grid-column: 1 / 17;
  grid-row: 1 / 2;
  min-height: 0;
  overflow: hidden;
}

/* 音乐控制卡片 8x8 (调整为给灵动岛让出空间) */
.focus-layout .music-card {
  grid-column: 1 / 9;
  grid-row: 2 / 10;
  min-height: 0;
  overflow: hidden;
}

/* 今日待办卡片 8x3 */
.focus-layout .todo-card {
  grid-column: 9 / 17;
  grid-row: 2 / 5;
  min-height: 0;
  overflow: hidden;
}

/* 订单状态卡片 4x2 */
.focus-layout .order-card {
  grid-column: 9 / 13;
  grid-row: 5 / 7;
  min-height: 0;
  overflow: hidden;
}

/* VPA小窗 2x2 */
.focus-layout .vpa-widget {
  grid-column: 15 / 17;
  grid-row: 7 / 9;
  min-height: 0;
  overflow: hidden;
}

/* 沉浸式桌面壁纸布局 */
.immersive-layout {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

/* 娱乐模式布局 (16x5 视频 + 底部卡片) */
.entertainment-layout {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
}

.entertainment-layout .video-row {
  flex: 3;
}

.entertainment-layout .card-video {
  width: 100%;
  height: 100%;
  min-height: 250px;
}

.entertainment-layout .bottom-row {
  flex: 1;
  display: flex;
  gap: 20px;
}

.entertainment-layout .card-small {
  flex: 1;
  min-height: 120px;
}

/* 极简模式布局 (雨夜模式) */
.minimal-layout {
  position: relative;
  height: 100%;
  padding: 20px;
}

.minimal-layout .dynamic-island-minimal {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 400px;
  height: 60px;
}

.minimal-layout .bottom-components {
  position: absolute;
  bottom: 40px;
  left: 40px;
  right: 40px;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.minimal-layout .vpa-minimal {
  width: 120px;
  height: 120px;
}

.minimal-layout .card-minimal {
  width: 300px;
  height: 120px;
}

/* 默认网格布局 */
.default-layout .layout-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.scene-card-slot {
  min-height: 200px;
}

.scene-transition-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
}

.transition-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  color: white;
  font-size: 18px;
}

.transition-content i {
  font-size: 48px;
  color: var(--scene-primary-color);
}

/* 场景切换动画 */
.scene-transition-enter-active,
.scene-transition-leave-active {
  transition: all 0.5s ease;
}

.scene-transition-enter-from {
  opacity: 0;
  transform: scale(0.8);
}

.scene-transition-leave-to {
  opacity: 0;
  transform: scale(1.2);
}

.scene-transition-enter-to,
.scene-transition-leave-from {
  opacity: 1;
  transform: scale(1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .scene-indicator {
    top: 10px;
    right: 10px;
    padding: 10px 15px;
    flex-direction: column;
    gap: 10px;
  }
  
  .scene-info {
    flex-direction: column;
    text-align: center;
  }
  
  .scene-description {
    margin-left: 0;
  }
  
  .scene-grid {
    grid-template-columns: 1fr;
  }
  
  .scene-content.standard,
  .scene-content.family,
  .scene-content.focus,
  .scene-content.entertainment,
  .scene-content.driving,
  .scene-content.parking {
    grid-template-columns: 1fr;
  }
}
</style>