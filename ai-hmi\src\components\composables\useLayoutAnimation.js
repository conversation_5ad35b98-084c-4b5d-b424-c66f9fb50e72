import { ref, computed } from 'vue'

/**
 * 布局动画管理组合式函数
 * 提供布局切换、组件移动等动画效果
 */
export function useLayoutAnimation(options = {}) {
  const {
    duration = 500,
    easing = 'ease',
    enableAnimations = true
  } = options
  
  const isAnimating = ref(false)
  const animationQueue = ref([])
  const currentAnimation = ref(null)
  
  // 动画状态
  const animationState = computed(() => ({
    isAnimating: isAnimating.value,
    currentAnimation: currentAnimation.value,
    queueLength: animationQueue.value.length
  }))
  
  // CSS动画类名
  const animationClasses = computed(() => ({
    'layout-animated': enableAnimations,
    'layout-animating': isAnimating.value
  }))
  
  // 执行布局切换动画
  const animateLayoutSwitch = (fromLayout, toLayout, callback) => {
    if (!enableAnimations) {
      callback?.()
      return
    }
    
    const animation = {
      type: 'layout-switch',
      from: fromLayout,
      to: toLayout,
      duration,
      callback
    }
    
    queueAnimation(animation)
  }
  
  // 执行组件移动动画
  const animateComponentMove = (componentId, fromPosition, toPosition, callback) => {
    if (!enableAnimations) {
      callback?.()
      return
    }
    
    const animation = {
      type: 'component-move',
      componentId,
      from: fromPosition,
      to: toPosition,
      duration,
      callback
    }
    
    queueAnimation(animation)
  }
  
  // 执行组件缩放动画
  const animateComponentScale = (componentId, fromScale, toScale, callback) => {
    if (!enableAnimations) {
      callback?.()
      return
    }
    
    const animation = {
      type: 'component-scale',
      componentId,
      from: fromScale,
      to: toScale,
      duration,
      callback
    }
    
    queueAnimation(animation)
  }
  
  // 执行渐显动画
  const animateFadeIn = (element, duration = duration, callback) => {
    if (!enableAnimations) {
      callback?.()
      return
    }
    
    const animation = {
      type: 'fade-in',
      element,
      duration,
      callback
    }
    
    queueAnimation(animation)
  }
  
  // 执行渐隐动画
  const animateFadeOut = (element, duration = duration, callback) => {
    if (!enableAnimations) {
      callback?.()
      return
    }
    
    const animation = {
      type: 'fade-out',
      element,
      duration,
      callback
    }
    
    queueAnimation(animation)
  }
  
  // 执行滑入动画
  const animateSlideIn = (element, direction = 'left', duration = duration, callback) => {
    if (!enableAnimations) {
      callback?.()
      return
    }
    
    const animation = {
      type: 'slide-in',
      element,
      direction,
      duration,
      callback
    }
    
    queueAnimation(animation)
  }
  
  // 执行滑出动画
  const animateSlideOut = (element, direction = 'right', duration = duration, callback) => {
    if (!enableAnimations) {
      callback?.()
      return
    }
    
    const animation = {
      type: 'slide-out',
      element,
      direction,
      duration,
      callback
    }
    
    queueAnimation(animation)
  }
  
  // 执行弹性动画
  const animateBounce = (element, intensity = 1, duration = duration, callback) => {
    if (!enableAnimations) {
      callback?.()
      return
    }
    
    const animation = {
      type: 'bounce',
      element,
      intensity,
      duration,
      callback
    }
    
    queueAnimation(animation)
  }
  
  // 添加动画到队列
  const queueAnimation = (animation) => {
    animationQueue.value.push(animation)
    processAnimationQueue()
  }
  
  // 处理动画队列
  const processAnimationQueue = () => {
    if (isAnimating.value || animationQueue.value.length === 0) {
      return
    }
    
    const animation = animationQueue.value.shift()
    executeAnimation(animation)
  }
  
  // 执行单个动画
  const executeAnimation = (animation) => {
    isAnimating.value = true
    currentAnimation.value = animation
    
    switch (animation.type) {
      case 'layout-switch':
        executeLayoutSwitch(animation)
        break
      case 'component-move':
        executeComponentMove(animation)
        break
      case 'component-scale':
        executeComponentScale(animation)
        break
      case 'fade-in':
        executeFadeIn(animation)
        break
      case 'fade-out':
        executeFadeOut(animation)
        break
      case 'slide-in':
        executeSlideIn(animation)
        break
      case 'slide-out':
        executeSlideOut(animation)
        break
      case 'bounce':
        executeBounce(animation)
        break
    }
  }
  
  // 执行布局切换动画
  const executeLayoutSwitch = (animation) => {
    // 添加CSS类触发动画
    document.body.classList.add('layout-transitioning')
    
    setTimeout(() => {
      document.body.classList.remove('layout-transitioning')
      completeAnimation(animation)
    }, animation.duration)
  }
  
  // 执行组件移动动画
  const executeComponentMove = (animation) => {
    const element = document.getElementById(animation.componentId)
    if (!element) {
      completeAnimation(animation)
      return
    }
    
    // 添加移动动画类
    element.style.transition = `all ${animation.duration}ms ${easing}`
    element.classList.add('component-moving')
    
    setTimeout(() => {
      element.classList.remove('component-moving')
      element.style.transition = ''
      completeAnimation(animation)
    }, animation.duration)
  }
  
  // 执行组件缩放动画
  const executeComponentScale = (animation) => {
    const element = document.getElementById(animation.componentId)
    if (!element) {
      completeAnimation(animation)
      return
    }
    
    element.style.transition = `transform ${animation.duration}ms ${easing}`
    element.style.transform = `scale(${animation.to})`
    
    setTimeout(() => {
      element.style.transform = ''
      element.style.transition = ''
      completeAnimation(animation)
    }, animation.duration)
  }
  
  // 执行渐显动画
  const executeFadeIn = (animation) => {
    const element = animation.element
    if (!element) {
      completeAnimation(animation)
      return
    }
    
    element.style.transition = `opacity ${animation.duration}ms ${easing}`
    element.style.opacity = '1'
    
    setTimeout(() => {
      element.style.transition = ''
      completeAnimation(animation)
    }, animation.duration)
  }
  
  // 执行渐隐动画
  const executeFadeOut = (animation) => {
    const element = animation.element
    if (!element) {
      completeAnimation(animation)
      return
    }
    
    element.style.transition = `opacity ${animation.duration}ms ${easing}`
    element.style.opacity = '0'
    
    setTimeout(() => {
      element.style.transition = ''
      completeAnimation(animation)
    }, animation.duration)
  }
  
  // 执行滑入动画
  const executeSlideIn = (animation) => {
    const element = animation.element
    if (!element) {
      completeAnimation(animation)
      return
    }
    
    const transforms = {
      left: 'translateX(-100%)',
      right: 'translateX(100%)',
      top: 'translateY(-100%)',
      bottom: 'translateY(100%)'
    }
    
    element.style.transition = `transform ${animation.duration}ms ${easing}`
    element.style.transform = transforms[animation.direction] || transforms.left
    
    // 强制重排
    element.offsetHeight
    
    element.style.transform = 'translateX(0)'
    
    setTimeout(() => {
      element.style.transition = ''
      element.style.transform = ''
      completeAnimation(animation)
    }, animation.duration)
  }
  
  // 执行滑出动画
  const executeSlideOut = (animation) => {
    const element = animation.element
    if (!element) {
      completeAnimation(animation)
      return
    }
    
    const transforms = {
      left: 'translateX(-100%)',
      right: 'translateX(100%)',
      top: 'translateY(-100%)',
      bottom: 'translateY(100%)'
    }
    
    element.style.transition = `transform ${animation.duration}ms ${easing}`
    element.style.transform = transforms[animation.direction] || transforms.right
    
    setTimeout(() => {
      element.style.transition = ''
      element.style.transform = ''
      completeAnimation(animation)
    }, animation.duration)
  }
  
  // 执行弹性动画
  const executeBounce = (animation) => {
    const element = animation.element
    if (!element) {
      completeAnimation(animation)
      return
    }
    
    const keyframes = [
      { transform: 'scale(1)', offset: 0 },
      { transform: `scale(${1 + 0.1 * animation.intensity})`, offset: 0.5 },
      { transform: 'scale(1)', offset: 1 }
    ]
    
    element.animate(keyframes, {
      duration: animation.duration,
      easing: 'ease-in-out'
    }).onfinish = () => {
      completeAnimation(animation)
    }
  }
  
  // 完成动画
  const completeAnimation = (animation) => {
    isAnimating.value = false
    currentAnimation.value = null
    
    // 执行回调
    animation.callback?.()
    
    // 处理队列中的下一个动画
    processAnimationQueue()
  }
  
  // 取消所有动画
  const cancelAllAnimations = () => {
    animationQueue.value = []
    isAnimating.value = false
    currentAnimation.value = null
  }
  
  // 设置动画启用状态
  const setAnimationsEnabled = (enabled) => {
    enableAnimations = enabled
  }
  
  // 设置动画持续时间
  const setAnimationDuration = (newDuration) => {
    duration = newDuration
  }
  
  // 设置动画缓动函数
  const setAnimationEasing = (newEasing) => {
    easing = newEasing
  }
  
  return {
    animationState,
    animationClasses,
    animateLayoutSwitch,
    animateComponentMove,
    animateComponentScale,
    animateFadeIn,
    animateFadeOut,
    animateSlideIn,
    animateSlideOut,
    animateBounce,
    cancelAllAnimations,
    setAnimationsEnabled,
    setAnimationDuration,
    setAnimationEasing
  }
}

/**
 * 默认导出
 */
export default useLayoutAnimation