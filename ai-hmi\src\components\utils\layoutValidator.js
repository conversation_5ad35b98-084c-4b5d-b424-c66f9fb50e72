/**
 * 布局验证器
 * 提供布局配置和组件位置的验证功能
 */
export class LayoutValidator {
  constructor() {
    this.validLayouts = [
      'default', 'family', 'focus', 'entertainment', 
      'minimal', 'immersive', 'driving', 'parking', 'emergency'
    ]
    
    this.validComponentSizes = [
      'small', 'medium', 'large', 'full', 'custom',
      'dynamicIsland', 'vpaWidget', 'kidEducation', 'music',
      'todo', 'pedia', 'orderStatus', 'news', 'entertainment',
      'fatigueWarning', 'emergencyContact', 'firstAid'
    ]
    
    this.validThemes = [
      'glass', 'solid', 'minimal', 'gradient'
    ]
    
    this.gridConstraints = {
      minWidth: 1,
      maxWidth: 16,
      minHeight: 1,
      maxHeight: 9,
      minGap: 0,
      maxGap: 50,
      minPadding: 0,
      maxPadding: 100
    }
  }
  
  /**
   * 验证布局模式
   */
  validateLayout(layout) {
    if (typeof layout !== 'string') {
      return {
        valid: false,
        errors: ['布局模式必须是字符串']
      }
    }
    
    if (!this.validLayouts.includes(layout)) {
      return {
        valid: false,
        errors: [`无效的布局模式: ${layout}。有效值: ${this.validLayouts.join(', ')}`]
      }
    }
    
    return {
      valid: true,
      errors: []
    }
  }
  
  /**
   * 验证组件尺寸
   */
  validateComponentSize(size) {
    if (typeof size !== 'string') {
      return {
        valid: false,
        errors: ['组件尺寸必须是字符串']
      }
    }
    
    if (!this.validComponentSizes.includes(size)) {
      return {
        valid: false,
        errors: [`无效的组件尺寸: ${size}。有效值: ${this.validComponentSizes.join(', ')}`]
      }
    }
    
    return {
      valid: true,
      errors: []
    }
  }
  
  /**
   * 验证主题
   */
  validateTheme(theme) {
    if (typeof theme !== 'string') {
      return {
        valid: false,
        errors: ['主题必须是字符串']
      }
    }
    
    if (!this.validThemes.includes(theme)) {
      return {
        valid: false,
        errors: [`无效的主题: ${theme}。有效值: ${this.validThemes.join(', ')}`]
      }
    }
    
    return {
      valid: true,
      errors: []
    }
  }
  
  /**
   * 验证网格配置
   */
  validateGridConfig(config) {
    const errors = []
    
    if (config.columns !== undefined) {
      if (typeof config.columns !== 'number' || config.columns < 1 || config.columns > 16) {
        errors.push('网格列数必须是1-16之间的数字')
      }
    }
    
    if (config.rows !== undefined) {
      if (typeof config.rows !== 'number' || config.rows < 1 || config.rows > 9) {
        errors.push('网格行数必须是1-9之间的数字')
      }
    }
    
    if (config.gap !== undefined) {
      if (typeof config.gap !== 'string' && typeof config.gap !== 'number') {
        errors.push('网格间距必须是字符串或数字')
      } else if (typeof config.gap === 'number') {
        if (config.gap < this.gridConstraints.minGap || config.gap > this.gridConstraints.maxGap) {
          errors.push(`网格间距必须在${this.gridConstraints.minGap}-${this.gridConstraints.maxGap}之间`)
        }
      }
    }
    
    if (config.padding !== undefined) {
      if (typeof config.padding !== 'string' && typeof config.padding !== 'number') {
        errors.push('网格内边距必须是字符串或数字')
      } else if (typeof config.padding === 'number') {
        if (config.padding < this.gridConstraints.minPadding || config.padding > this.gridConstraints.maxPadding) {
          errors.push(`网格内边距必须在${this.gridConstraints.minPadding}-${this.gridConstraints.maxPadding}之间`)
        }
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
  
  /**
   * 验证组件位置
   */
  validateComponentPosition(position, gridConfig = { columns: 16, rows: 9 }) {
    const errors = []
    
    if (!position || typeof position !== 'object') {
      return {
        valid: false,
        errors: ['组件位置必须是对象']
      }
    }
    
    if (position.x === undefined || position.y === undefined) {
      errors.push('组件位置必须包含x和y坐标')
    } else {
      if (typeof position.x !== 'number' || position.x < 1 || position.x > gridConfig.columns) {
        errors.push(`x坐标必须在1-${gridConfig.columns}之间`)
      }
      
      if (typeof position.y !== 'number' || position.y < 1 || position.y > gridConfig.rows) {
        errors.push(`y坐标必须在1-${gridConfig.rows}之间`)
      }
    }
    
    if (position.width !== undefined) {
      if (typeof position.width !== 'number' || position.width < 1) {
        errors.push('宽度必须是大于0的数字')
      } else if (position.x + position.width - 1 > gridConfig.columns) {
        errors.push('组件超出网格右边界')
      }
    }
    
    if (position.height !== undefined) {
      if (typeof position.height !== 'number' || position.height < 1) {
        errors.push('高度必须是大于0的数字')
      } else if (position.y + position.height - 1 > gridConfig.rows) {
        errors.push('组件超出网格下边界')
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
  
  /**
   * 验证组件配置
   */
  validateComponentConfig(component) {
    const errors = []
    
    if (!component || typeof component !== 'object') {
      return {
        valid: false,
        errors: ['组件配置必须是对象']
      }
    }
    
    if (!component.id || typeof component.id !== 'string') {
      errors.push('组件必须包含字符串类型的id')
    }
    
    if (!component.type || typeof component.type !== 'string') {
      errors.push('组件必须包含字符串类型的type')
    }
    
    // 验证尺寸
    if (component.size) {
      const sizeValidation = this.validateComponentSize(component.size)
      if (!sizeValidation.valid) {
        errors.push(...sizeValidation.errors)
      }
    }
    
    // 验证位置
    if (component.position) {
      const positionValidation = this.validateComponentPosition(component.position)
      if (!positionValidation.valid) {
        errors.push(...positionValidation.errors)
      }
    }
    
    // 验证主题
    if (component.theme) {
      const themeValidation = this.validateTheme(component.theme)
      if (!themeValidation.valid) {
        errors.push(...themeValidation.errors)
      }
    }
    
    // 验证自定义尺寸
    if (component.customSize) {
      const customSizeErrors = this.validateCustomSize(component.customSize)
      if (customSizeErrors.length > 0) {
        errors.push(...customSizeErrors)
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
  
  /**
   * 验证自定义尺寸
   */
  validateCustomSize(customSize) {
    const errors = []
    
    if (!customSize || typeof customSize !== 'object') {
      return ['自定义尺寸必须是对象']
    }
    
    if (customSize.width === undefined || customSize.height === undefined) {
      errors.push('自定义尺寸必须包含width和height')
    } else {
      if (typeof customSize.width !== 'number' || customSize.width < 1) {
        errors.push('宽度必须是大于0的数字')
      }
      
      if (typeof customSize.height !== 'number' || customSize.height < 1) {
        errors.push('高度必须是大于0的数字')
      }
    }
    
    return errors
  }
  
  /**
   * 验证布局冲突
   */
  validateLayoutConflicts(components, gridConfig = { columns: 16, rows: 9 }) {
    const conflicts = []
    
    for (let i = 0; i < components.length; i++) {
      for (let j = i + 1; j < components.length; j++) {
        const component1 = components[i]
        const component2 = components[j]
        
        if (this.isOverlapping(component1, component2)) {
          conflicts.push({
            type: 'overlap',
            component1: component1.id,
            component2: component2.id,
            message: `组件 ${component1.id} 和 ${component2.id} 位置重叠`
          })
        }
      }
    }
    
    return {
      hasConflicts: conflicts.length > 0,
      conflicts
    }
  }
  
  /**
   * 检查两个组件是否重叠
   */
  isOverlapping(component1, component2) {
    const pos1 = component1.position || { x: 1, y: 1, width: 1, height: 1 }
    const pos2 = component2.position || { x: 1, y: 1, width: 1, height: 1 }
    
    return !(pos1.x + (pos1.width || 1) - 1 < pos2.x || 
             pos2.x + (pos2.width || 1) - 1 < pos1.x || 
             pos1.y + (pos1.height || 1) - 1 < pos2.y || 
             pos2.y + (pos2.height || 1) - 1 < pos1.y)
  }
  
  /**
   * 验证完整布局配置
   */
  validateFullLayout(layoutConfig) {
    const errors = []
    
    if (!layoutConfig || typeof layoutConfig !== 'object') {
      return {
        valid: false,
        errors: ['布局配置必须是对象']
      }
    }
    
    // 验证布局模式
    if (layoutConfig.layout) {
      const layoutValidation = this.validateLayout(layoutConfig.layout)
      if (!layoutValidation.valid) {
        errors.push(...layoutValidation.errors)
      }
    }
    
    // 验证网格配置
    if (layoutConfig.gridConfig) {
      const gridValidation = this.validateGridConfig(layoutConfig.gridConfig)
      if (!gridValidation.valid) {
        errors.push(...gridValidation.errors)
      }
    }
    
    // 验证组件列表
    if (layoutConfig.components) {
      if (!Array.isArray(layoutConfig.components)) {
        errors.push('组件列表必须是数组')
      } else {
        layoutConfig.components.forEach((component, index) => {
          const componentValidation = this.validateComponentConfig(component)
          if (!componentValidation.valid) {
            errors.push(`组件 ${index} (${component.id || 'unnamed'}): ${componentValidation.errors.join(', ')}`)
          }
        })
        
        // 验证布局冲突
        const conflictValidation = this.validateLayoutConflicts(layoutConfig.components)
        if (conflictValidation.hasConflicts) {
          conflictValidation.conflicts.forEach(conflict => {
            errors.push(conflict.message)
          })
        }
      }
    }
    
    return {
      valid: errors.length === 0,
      errors,
      summary: this.generateValidationSummary(errors)
    }
  }
  
  /**
   * 生成验证摘要
   */
  generateValidationSummary(errors) {
    if (errors.length === 0) {
      return '布局配置验证通过'
    }
    
    const errorTypes = {}
    errors.forEach(error => {
      const type = error.includes('布局模式') ? 'layout' :
                  error.includes('组件') ? 'component' :
                  error.includes('网格') ? 'grid' :
                  error.includes('位置') ? 'position' :
                  error.includes('重叠') ? 'conflict' : 'other'
      
      errorTypes[type] = (errorTypes[type] || 0) + 1
    })
    
    return `发现 ${errors.length} 个错误: ` + 
           Object.entries(errorTypes)
             .map(([type, count]) => `${type}(${count})`)
             .join(', ')
  }
  
  /**
   * 修复常见的布局问题
   */
  fixLayoutIssues(layoutConfig) {
    const fixed = { ...layoutConfig }
    
    // 修复组件位置
    if (fixed.components) {
      fixed.components = fixed.components.map(component => {
        const fixedComponent = { ...component }
        
        // 确保有位置信息
        if (!fixedComponent.position) {
          fixedComponent.position = { x: 1, y: 1 }
        }
        
        // 确保位置在有效范围内
        fixedComponent.position.x = Math.max(1, Math.min(fixedComponent.position.x || 1, 16))
        fixedComponent.position.y = Math.max(1, Math.min(fixedComponent.position.y || 1, 9))
        
        return fixedComponent
      })
    }
    
    return fixed
  }
}

/**
 * 默认导出实例
 */
export const layoutValidator = new LayoutValidator()

/**
 * 默认导出类
 */
export default LayoutValidator