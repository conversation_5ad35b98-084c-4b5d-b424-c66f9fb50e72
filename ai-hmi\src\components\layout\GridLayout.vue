<template>
  <div 
    :class="['grid-layout', layoutClass]"
    :style="gridStyles"
  >
    <slot />
  </div>
</template>

<script>
import { computed, provide, ref, watch } from 'vue'
import { useLayoutStore } from '@/store/modules/layout'

export default {
  name: 'GridLayout',
  props: {
    // 布局模式
    layout: {
      type: String,
      default: 'default',
      validator: (value) => [
        'default', 'family', 'focus', 'entertainment', 
        'minimal', 'immersive', 'driving', 'parking', 'emergency'
      ].includes(value)
    },
    
    // 自定义网格配置
    columns: {
      type: Number,
      default: 16
    },
    
    rows: {
      type: Number,
      default: 9
    },
    
    // 网格间距
    gap: {
      type: String,
      default: 'min(1vw, 10px)'
    },
    
    // 内边距
    padding: {
      type: String,
      default: 'min(1vw, 10px)'
    },
    
    // 是否启用响应式
    responsive: {
      type: Boolean,
      default: true
    }
  },
  
  setup(props) {
    const layoutStore = useLayoutStore()
    
    // 网格样式
    const gridStyles = computed(() => {
      const baseStyles = {
        display: 'grid',
        gap: props.gap,
        padding: props.padding,
        width: '100%',
        height: '100vh',
        boxSizing: 'border-box',
        overflow: 'hidden'
      }
      
      // 根据布局模式设置网格模板
      switch (props.layout) {
        case 'immersive':
          return {
            ...baseStyles,
            gridTemplateColumns: '1fr',
            gridTemplateRows: '1fr'
          }
        
        case 'minimal':
          return {
            ...baseStyles,
            gridTemplateColumns: 'repeat(16, 1fr)',
            gridTemplateRows: 'repeat(9, 1fr)',
            position: 'relative'
          }
          
        case 'entertainment':
          return {
            ...baseStyles,
            display: 'flex',
            flexDirection: 'column',
            gap: props.gap
          }
          
        default:
          return {
            ...baseStyles,
            gridTemplateColumns: `repeat(${props.columns}, 1fr)`,
            gridTemplateRows: `repeat(${props.rows}, 1fr)`
          }
      }
    })
    
    // 布局类名
    const layoutClass = computed(() => {
      if (props.layout === 'entertainment') {
        return 'layout-flex'
      }
      return `layout-${props.layout}`
    })
    
    // 提供布局上下文给子组件
    provide('layoutContext', {
      layout: props.layout,
      gridSize: {
        columns: props.columns,
        rows: props.rows
      }
    })
    
    // 监听布局变化
    watch(() => props.layout, (newLayout) => {
      layoutStore.switchLayout(newLayout)
    }, { immediate: true })
    
    return {
      gridStyles,
      layoutClass
    }
  }
}
</script>

<style scoped>
.grid-layout {
  position: relative;
  background: transparent;
  transition: all 0.3s ease;
}

/* 默认布局样式 */
.layout-default {
  grid-template-columns: repeat(16, 1fr);
  grid-template-rows: repeat(9, 1fr);
}

/* 家庭出行布局 */
.layout-family {
  grid-template-columns: repeat(16, 1fr);
  grid-template-rows: repeat(9, 1fr);
}

/* 专注通勤布局 */
.layout-focus {
  grid-template-columns: repeat(16, 1fr);
  grid-template-rows: repeat(9, 1fr);
}

/* 驾驶布局 */
.layout-driving {
  grid-template-columns: repeat(16, 1fr);
  grid-template-rows: repeat(9, 1fr);
}

/* 泊车布局 */
.layout-parking {
  grid-template-columns: repeat(12, 1fr);
  grid-template-rows: repeat(9, 1fr);
}

/* 紧急布局 */
.layout-emergency {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

/* Flex布局（用于娱乐模式等） */
.layout-flex {
  flex-direction: column;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-layout {
    grid-template-columns: repeat(8, 1fr) !important;
    grid-template-rows: repeat(12, 1fr) !important;
  }
  
  .layout-parking {
    grid-template-columns: repeat(8, 1fr) !important;
    grid-template-rows: repeat(10, 1fr) !important;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .grid-layout {
    grid-template-columns: repeat(12, 1fr) !important;
    grid-template-rows: repeat(10, 1fr) !important;
  }
}
</style>