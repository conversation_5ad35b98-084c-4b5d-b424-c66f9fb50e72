/**
 * VPA动画管理器
 * 支持GIF、Live2D、CSS动画等多种动画格式
 */
export class VPAAnimationManager {
  constructor() {
    this.animationCache = new Map()
    this.loadedResources = new Map()
    this.currentAnimation = null
    this.animationQueue = []
    this.isPlaying = false
    this.animationFrame = null
    
    // 支持的动画类型
    this.supportedTypes = {
      gif: {
        mimeTypes: ['image/gif'],
        extensions: ['.gif'],
        loader: this.loadGIFAnimation.bind(this)
      },
      live2d: {
        mimeTypes: ['application/x-live2d'],
        extensions: ['.moc3', '.model3.json'],
        loader: this.loadLive2DAnimation.bind(this)
      },
      css: {
        mimeTypes: ['text/css'],
        extensions: ['.css'],
        loader: this.loadCSSAnimation.bind(this)
      },
      sprite: {
        mimeTypes: ['image/png', 'image/jpeg'],
        extensions: ['.png', '.jpg', '.jpeg'],
        loader: this.loadSpriteAnimation.bind(this)
      }
    }
    
    // 默认动画配置
    this.defaultConfig = {
      duration: 3000,
      loop: true,
      autoplay: true,
      fadeIn: 300,
      fadeOut: 300,
      scale: 1.0,
      opacity: 1.0
    }
  }
  
  /**
   * 加载动画资源
   */
  async loadAnimation(resourceUrl, type = 'auto') {
    try {
      // 检查缓存
      if (this.animationCache.has(resourceUrl)) {
        return this.animationCache.get(resourceUrl)
      }
      
      // 自动检测类型
      if (type === 'auto') {
        type = this.detectAnimationType(resourceUrl)
      }
      
      // 验证类型支持
      if (!this.supportedTypes[type]) {
        throw new Error(`不支持的动画类型: ${type}`)
      }
      
      // 加载资源
      const loader = this.supportedTypes[type].loader
      const animation = await loader(resourceUrl)
      
      // 缓存结果
      this.animationCache.set(resourceUrl, animation)
      this.loadedResources.set(resourceUrl, { type, animation })
      
      console.log(`动画资源加载成功: ${resourceUrl} (${type})`)
      return animation
      
    } catch (error) {
      console.error(`动画资源加载失败: ${resourceUrl}`, error)
      throw error
    }
  }
  
  /**
   * 检测动画类型
   */
  detectAnimationType(resourceUrl) {
    const extension = resourceUrl.split('.').pop().toLowerCase()
    
    for (const [type, config] of Object.entries(this.supportedTypes)) {
      if (config.extensions.includes(`.${extension}`)) {
        return type
      }
    }
    
    return 'css' // 默认CSS动画
  }
  
  /**
   * 加载GIF动画
   */
  async loadGIFAnimation(url) {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        const animation = {
          type: 'gif',
          element: img,
          width: img.width,
          height: img.height,
          duration: this.getGIFDuration(img),
          isLooping: true
        }
        resolve(animation)
      }
      img.onerror = () => reject(new Error(`GIF加载失败: ${url}`))
      img.src = url
    })
  }
  
  /**
   * 加载Live2D动画
   */
  async loadLive2DAnimation(url) {
    // 这里需要集成Live2D SDK
    // 返回模拟的Live2D动画对象
    return {
      type: 'live2d',
      modelPath: url,
      isLoaded: false,
      load: async () => {
        // 实际项目中这里会加载Live2D模型
        console.log(`加载Live2D模型: ${url}`)
        return true
      }
    }
  }
  
  /**
   * 加载CSS动画
   */
  async loadCSSAnimation(url) {
    try {
      const response = await fetch(url)
      const cssText = await response.text()
      
      // 创建样式元素
      const style = document.createElement('style')
      style.textContent = cssText
      document.head.appendChild(style)
      
      return {
        type: 'css',
        styleElement: style,
        cssText: cssText
      }
    } catch (error) {
      throw new Error(`CSS动画加载失败: ${url}`)
    }
  }
  
  /**
   * 加载精灵动画
   */
  async loadSpriteAnimation(url) {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        const animation = {
          type: 'sprite',
          element: img,
          width: img.width,
          height: img.height,
          frameWidth: img.width / 4, // 假设4帧
          frameHeight: img.height,
          frameCount: 4,
          currentFrame: 0
        }
        resolve(animation)
      }
      img.onerror = () => reject(new Error(`精灵图加载失败: ${url}`))
      img.src = url
    })
  }
  
  /**
   * 播放动画
   */
  async playAnimation(resourceUrl, config = {}) {
    try {
      // 停止当前动画
      this.stopAnimation()
      
      // 加载动画资源
      const animation = await this.loadAnimation(resourceUrl)
      
      // 合并配置
      const finalConfig = { ...this.defaultConfig, ...config }
      
      // 开始播放
      this.currentAnimation = {
        resource: resourceUrl,
        animation: animation,
        config: finalConfig,
        startTime: Date.now()
      }
      
      this.isPlaying = true
      
      // 根据动画类型播放
      switch (animation.type) {
        case 'gif':
          this.playGIFAnimation(animation, finalConfig)
          break
        case 'css':
          this.playCSSAnimation(animation, finalConfig)
          break
        case 'sprite':
          this.playSpriteAnimation(animation, finalConfig)
          break
        case 'live2d':
          this.playLive2DAnimation(animation, finalConfig)
          break
      }
      
      console.log(`开始播放动画: ${resourceUrl}`)
      
    } catch (error) {
      console.error('动画播放失败:', error)
      throw error
    }
  }
  
  /**
   * 播放GIF动画
   */
  playGIFAnimation(animation, config) {
    // GIF会自动播放，我们只需要管理生命周期
    if (config.duration && config.loop === false) {
      setTimeout(() => {
        this.stopAnimation()
      }, config.duration)
    }
  }
  
  /**
   * 播放CSS动画
   */
  playCSSAnimation(animation, config) {
    // CSS动画通过CSS类控制，这里只需要管理时间
    if (config.duration && config.loop === false) {
      setTimeout(() => {
        this.stopAnimation()
      }, config.duration)
    }
  }
  
  /**
   * 播放精灵动画
   */
  playSpriteAnimation(animation, config) {
    let frame = 0
    const frameInterval = 1000 / (config.frameRate || 10)
    
    const animate = () => {
      if (!this.isPlaying) return
      
      animation.currentFrame = frame
      frame = (frame + 1) % animation.frameCount
      
      // 检查是否需要停止
      if (config.loop === false && frame === 0) {
        this.stopAnimation()
        return
      }
      
      setTimeout(animate, frameInterval)
    }
    
    animate()
  }
  
  /**
   * 播放Live2D动画
   */
  playLive2DAnimation(animation, config) {
    // Live2D动画通过SDK控制
    console.log('播放Live2D动画')
  }
  
  /**
   * 停止动画
   */
  stopAnimation() {
    if (this.currentAnimation) {
      const { animation, config } = this.currentAnimation
      
      // 执行淡出效果
      if (config.fadeOut > 0) {
        // 这里可以添加淡出逻辑
      }
      
      this.currentAnimation = null
      this.isPlaying = false
      
      if (this.animationFrame) {
        cancelAnimationFrame(this.animationFrame)
        this.animationFrame = null
      }
      
      console.log('动画已停止')
    }
  }
  
  /**
   * 暂停动画
   */
  pauseAnimation() {
    this.isPlaying = false
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame)
      this.animationFrame = null
    }
  }
  
  /**
   * 恢复动画
   */
  resumeAnimation() {
    if (this.currentAnimation && !this.isPlaying) {
      this.isPlaying = true
      const { animation, config } = this.currentAnimation
      
      // 根据动画类型恢复播放
      switch (animation.type) {
        case 'sprite':
          this.playSpriteAnimation(animation, config)
          break
        case 'live2d':
          this.playLive2DAnimation(animation, config)
          break
      }
    }
  }
  
  /**
   * 队列播放动画
   */
  queueAnimation(resourceUrl, config = {}) {
    this.animationQueue.push({ resourceUrl, config })
    
    if (!this.isPlaying) {
      this.playNextInQueue()
    }
  }
  
  /**
   * 播放队列中的下一个动画
   */
  playNextInQueue() {
    if (this.animationQueue.length > 0) {
      const next = this.animationQueue.shift()
      this.playAnimation(next.resourceUrl, next.config).then(() => {
        this.playNextInQueue()
      })
    }
  }
  
  /**
   * 获取动画状态
   */
  getAnimationStatus() {
    return {
      isPlaying: this.isPlaying,
      currentAnimation: this.currentAnimation,
      queueLength: this.animationQueue.length,
      loadedResources: this.loadedResources.size
    }
  }
  
  /**
   * 预加载动画资源
   */
  async preloadAnimations(resourceUrls) {
    const promises = resourceUrls.map(url => this.loadAnimation(url))
    return Promise.allSettled(promises)
  }
  
  /**
   * 清理资源
   */
  cleanup() {
    this.stopAnimation()
    this.animationCache.clear()
    this.loadedResources.clear()
    this.animationQueue = []
    
    // 清理DOM元素
    this.loadedResources.forEach((resource) => {
      if (resource.animation.styleElement) {
        document.head.removeChild(resource.animation.styleElement)
      }
    })
  }
  
  /**
   * 获取GIF持续时间（估算）
   */
  getGIFDuration(img) {
    // 这里可以通过解析GIF文件来获取准确时长
    // 暂时返回默认值
    return 3000
  }
  
  /**
   * 创建动画元素
   */
  createAnimationElement(animation, container) {
    const element = document.createElement('div')
    element.className = 'vpa-animation-container'
    
    switch (animation.type) {
      case 'gif':
      case 'sprite':
        element.appendChild(animation.element)
        break
      case 'live2d':
        element.innerHTML = '<div class="live2d-container"></div>'
        break
      case 'css':
        element.innerHTML = '<div class="css-animation-container"></div>'
        break
    }
    
    if (container) {
      container.appendChild(element)
    }
    
    return element
  }
}

/**
 * 默认导出实例
 */
export const vpaAnimationManager = new VPAAnimationManager()

/**
 * 默认导出类
 */
export default VPAAnimationManager