/**
 * 主题管理器
 * 提供主题切换、颜色管理和样式生成功能
 */
export class ThemeManager {
  constructor() {
    this.themes = {
      // 玻璃态主题
      glass: {
        name: '玻璃态',
        colors: {
          primary: '#4a90e2',
          secondary: '#7ed321',
          background: 'rgba(255, 255, 255, 0.1)',
          surface: 'rgba(255, 255, 255, 0.05)',
          text: '#ffffff',
          border: 'rgba(255, 255, 255, 0.2)',
          shadow: 'rgba(0, 0, 0, 0.1)'
        },
        styles: {
          backdropFilter: 'blur(10px)',
          borderRadius: '15px',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)'
        }
      },
      
      // 实心主题
      solid: {
        name: '实心',
        colors: {
          primary: '#4a90e2',
          secondary: '#7ed321',
          background: '#2c3e50',
          surface: '#34495e',
          text: '#ffffff',
          border: 'transparent',
          shadow: 'rgba(0, 0, 0, 0.2)'
        },
        styles: {
          backdropFilter: 'none',
          borderRadius: '15px',
          border: 'none',
          boxShadow: '0 4px 15px rgba(0, 0, 0, 0.2)'
        }
      },
      
      // 极简主题
      minimal: {
        name: '极简',
        colors: {
          primary: '#4a90e2',
          secondary: '#7ed321',
          background: 'transparent',
          surface: 'transparent',
          text: '#ffffff',
          border: '#4a90e2',
          shadow: 'transparent'
        },
        styles: {
          backdropFilter: 'none',
          borderRadius: '0',
          border: '1px solid #4a90e2',
          boxShadow: 'none'
        }
      },
      
      // 渐变主题
      gradient: {
        name: '渐变',
        colors: {
          primary: '#4a90e2',
          secondary: '#7ed321',
          background: 'linear-gradient(135deg, #4a90e2 0%, #7ed321 100%)',
          surface: 'rgba(255, 255, 255, 0.1)',
          text: '#ffffff',
          border: 'transparent',
          shadow: 'rgba(0, 0, 0, 0.15)'
        },
        styles: {
          backdropFilter: 'none',
          borderRadius: '15px',
          border: 'none',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)'
        }
      }
    }
    
    this.currentTheme = 'glass'
    this.customThemes = new Map()
    this.colorSchemes = {
      light: {
        background: '#ffffff',
        surface: '#f8f9fa',
        text: '#212529',
        border: '#dee2e6',
        shadow: 'rgba(0, 0, 0, 0.1)'
      },
      dark: {
        background: '#212529',
        surface: '#343a40',
        text: '#ffffff',
        border: '#495057',
        shadow: 'rgba(0, 0, 0, 0.3)'
      },
      warm: {
        background: '#fff8f0',
        surface: '#fff3e0',
        text: '#5d4037',
        border: '#ffcc02',
        shadow: 'rgba(255, 152, 0, 0.2)'
      },
      cool: {
        background: '#e8f5e8',
        surface: '#f1f8f1',
        text: '#2e7d32',
        border: '#81c784',
        shadow: 'rgba(46, 125, 50, 0.2)'
      }
    }
  }
  
  /**
   * 获取主题
   */
  getTheme(themeName) {
    return this.themes[themeName] || this.themes.glass
  }
  
  /**
   * 获取当前主题
   */
  getCurrentTheme() {
    return this.themes[this.currentTheme]
  }
  
  /**
   * 设置当前主题
   */
  setCurrentTheme(themeName) {
    if (this.themes[themeName]) {
      this.currentTheme = themeName
      this.applyTheme(themeName)
      return true
    }
    return false
  }
  
  /**
   * 应用主题到DOM
   */
  applyTheme(themeName) {
    const theme = this.getTheme(themeName)
    const root = document.documentElement
    
    // 设置CSS变量
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--theme-${key}`, value)
    })
    
    // 设置样式变量
    Object.entries(theme.styles).forEach(([key, value]) => {
      root.style.setProperty(`--theme-${key}`, value)
    })
    
    // 触发主题变更事件
    window.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme: themeName } }))
  }
  
  /**
   * 创建自定义主题
   */
  createCustomTheme(name, config) {
    const customTheme = {
      name: config.name || name,
      colors: {
        primary: config.colors?.primary || '#4a90e2',
        secondary: config.colors?.secondary || '#7ed321',
        background: config.colors?.background || 'rgba(255, 255, 255, 0.1)',
        surface: config.colors?.surface || 'rgba(255, 255, 255, 0.05)',
        text: config.colors?.text || '#ffffff',
        border: config.colors?.border || 'rgba(255, 255, 255, 0.2)',
        shadow: config.colors?.shadow || 'rgba(0, 0, 0, 0.1)'
      },
      styles: {
        backdropFilter: config.styles?.backdropFilter || 'blur(10px)',
        borderRadius: config.styles?.borderRadius || '15px',
        border: config.styles?.border || '1px solid rgba(255, 255, 255, 0.2)',
        boxShadow: config.styles?.boxShadow || '0 4px 20px rgba(0, 0, 0, 0.1)'
      }
    }
    
    this.customThemes.set(name, customTheme)
    return customTheme
  }
  
  /**
   * 获取自定义主题
   */
  getCustomTheme(name) {
    return this.customThemes.get(name)
  }
  
  /**
   * 删除自定义主题
   */
  deleteCustomTheme(name) {
    return this.customThemes.delete(name)
  }
  
  /**
   * 获取所有主题
   */
  getAllThemes() {
    return {
      ...this.themes,
      ...Object.fromEntries(this.customThemes)
    }
  }
  
  /**
   * 生成主题CSS
   */
  generateThemeCSS(themeName) {
    const theme = this.getTheme(themeName)
    if (!theme) return ''
    
    let css = `/* Theme: ${theme.name} */\n`
    css += `:root {\n`
    
    // 颜色变量
    Object.entries(theme.colors).forEach(([key, value]) => {
      css += `  --theme-${key}: ${value};\n`
    })
    
    // 样式变量
    Object.entries(theme.styles).forEach(([key, value]) => {
      css += `  --theme-${key}: ${value};\n`
    })
    
    css += `}\n\n`
    
    // 主题类样式
    css += `.theme-${themeName} {\n`
    css += `  background: var(--theme-background);\n`
    css += `  color: var(--theme-text);\n`
    css += `  border-radius: var(--theme-borderRadius);\n`
    css += `  border: var(--theme-border);\n`
    css += `  box-shadow: var(--theme-boxShadow);\n`
    css += `  backdrop-filter: var(--theme-backdropFilter);\n`
    css += `}\n`
    
    return css
  }
  
  /**
   * 生成所有主题CSS
   */
  generateAllThemesCSS() {
    let css = '/* Generated Theme Styles */\n\n'
    
    // 内置主题
    Object.keys(this.themes).forEach(themeName => {
      css += this.generateThemeCSS(themeName) + '\n'
    })
    
    // 自定义主题
    this.customThemes.forEach((theme, name) => {
      css += this.generateThemeCSS(name) + '\n'
    })
    
    return css
  }
  
  /**
   * 混合两个主题
   */
  blendThemes(theme1Name, theme2Name, ratio = 0.5) {
    const theme1 = this.getTheme(theme1Name)
    const theme2 = this.getTheme(theme2Name)
    
    if (!theme1 || !theme2) return null
    
    const blendColor = (color1, color2) => {
      // 简化的颜色混合（实际应用中需要更复杂的颜色空间转换）
      return ratio > 0.5 ? color1 : color2
    }
    
    return {
      name: `${theme1.name} + ${theme2.name}`,
      colors: {
        primary: blendColor(theme1.colors.primary, theme2.colors.primary),
        secondary: blendColor(theme1.colors.secondary, theme2.colors.secondary),
        background: blendColor(theme1.colors.background, theme2.colors.background),
        surface: blendColor(theme1.colors.surface, theme2.colors.surface),
        text: blendColor(theme1.colors.text, theme2.colors.text),
        border: blendColor(theme1.colors.border, theme2.colors.border),
        shadow: blendColor(theme1.colors.shadow, theme2.colors.shadow)
      },
      styles: {
        ...theme1.styles
      }
    }
  }
  
  /**
   * 根据时间自动切换主题
   */
  autoThemeByTime() {
    const hour = new Date().getHours()
    
    if (hour >= 6 && hour < 12) {
      return 'glass' // 早晨
    } else if (hour >= 12 && hour < 18) {
      return 'solid' // 下午
    } else if (hour >= 18 && hour < 22) {
      return 'gradient' // 晚上
    } else {
      return 'minimal' // 深夜
    }
  }
  
  /**
   * 根据场景选择主题
   */
  getThemeForScene(scene) {
    const sceneThemeMap = {
      family: 'glass',
      focus: 'minimal',
      entertainment: 'gradient',
      minimal: 'minimal',
      immersive: 'solid',
      driving: 'solid',
      parking: 'glass',
      emergency: 'solid'
    }
    
    return sceneThemeMap[scene] || 'glass'
  }
  
  /**
   * 验证颜色值
   */
  isValidColor(color) {
    const colorRegex = /^(#([0-9A-Fa-f]{3}){1,2}|rgba?\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*(?:,\s*[\d.]+\s*)?\)|hsla?\(\s*\d+\s*,\s*[\d.]+\s*,\s*[\d.]+\s*(?:,\s*[\d.]+\s*)?\)|transparent|inherit|currentColor)$/
    return colorRegex.test(color)
  }
  
  /**
   * 获取主题颜色对比度
   */
  getColorContrast(color1, color2) {
    // 简化的对比度计算（实际应用中需要更精确的计算）
    return Math.abs(parseInt(color1.slice(1, 3), 16) - parseInt(color2.slice(1, 3), 16))
  }
  
  /**
   * 生成主题变体
   */
  generateThemeVariant(themeName, variant) {
    const theme = this.getTheme(themeName)
    if (!theme) return null
    
    const variants = {
      light: {
        background: 'rgba(255, 255, 255, 0.9)',
        surface: 'rgba(255, 255, 255, 0.7)',
        text: '#212529'
      },
      dark: {
        background: 'rgba(0, 0, 0, 0.8)',
        surface: 'rgba(0, 0, 0, 0.6)',
        text: '#ffffff'
      },
      warm: {
        primary: '#ff6b6b',
        secondary: '#feca57',
        background: 'rgba(255, 235, 205, 0.3)'
      },
      cool: {
        primary: '#4834d4',
        secondary: '#686de0',
        background: 'rgba(230, 230, 250, 0.3)'
      }
    }
    
    const variantConfig = variants[variant]
    if (!variantConfig) return null
    
    return {
      name: `${theme.name} (${variant})`,
      colors: {
        ...theme.colors,
        ...variantConfig
      },
      styles: { ...theme.styles }
    }
  }
  
  /**
   * 导出主题配置
   */
  exportTheme(themeName) {
    const theme = this.getTheme(themeName)
    if (!theme) return null
    
    return JSON.stringify(theme, null, 2)
  }
  
  /**
   * 导入主题配置
   */
  importTheme(themeConfig) {
    try {
      const config = JSON.parse(themeConfig)
      if (config.name && config.colors) {
        const themeName = config.name.toLowerCase().replace(/\s+/g, '_')
        this.createCustomTheme(themeName, config)
        return true
      }
    } catch (error) {
      console.error('导入主题失败:', error)
    }
    return false
  }
}

/**
 * 默认导出实例
 */
export const themeManager = new ThemeManager()

/**
 * 默认导出类
 */
export default ThemeManager