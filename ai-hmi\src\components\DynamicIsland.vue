<template>
  <div 
    :class="['dynamic-island', `size-${size}`, `state-${currentState}`, { 'expanded': isExpanded }]"
    :style="islandStyles"
    @click="handleIslandClick"
  >
    <!-- 主要内容区域 -->
    <div class="island-content">
      <!-- 左侧图标/状态 -->
      <div class="island-left">
        <div class="status-icon" :class="currentPriorityItem?.iconClass">
          <i :class="currentPriorityItem?.icon || 'fas fa-circle'"></i>
        </div>
        <div v-if="showText" class="status-text">
          {{ currentPriorityItem?.text || '就绪' }}
        </div>
      </div>
      
      <!-- 中间内容（扩展时显示） -->
      <div v-if="isExpanded" class="island-center">
        <div class="expanded-content">
          <div class="content-title">{{ expandedContent.title }}</div>
          <div class="content-details">{{ expandedContent.details }}</div>
        </div>
      </div>
      
      <!-- 右侧操作/时间 -->
      <div class="island-right">
        <div v-if="showTime" class="current-time">
          {{ currentTime }}
        </div>
        <div v-if="hasActions" class="action-indicator">
          <i class="fas fa-chevron-right"></i>
        </div>
      </div>
    </div>
    
    <!-- 动态指示器 -->
    <div v-if="showIndicator" class="activity-indicator">
      <div class="indicator-dot" :class="indicatorState"></div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'

export default {
  name: 'DynamicIsland',
  props: {
    // 尺寸模式
    size: {
      type: String,
      default: 'normal',
      validator: (value) => ['compact', 'normal', 'expanded'].includes(value)
    },
    
    // 优先级列表
    priority: {
      type: Array,
      default: () => ['navigation', 'music', 'calls', 'notifications']
    },
    
    // 主题颜色
    themeColors: {
      type: Object,
      default: () => ({
        primary: '#000000',
        secondary: '#333333',
        background: 'rgba(0, 0, 0, 0.8)',
        text: '#ffffff'
      })
    },
    
    // 显示配置
    showTime: {
      type: Boolean,
      default: true
    },
    
    showText: {
      type: Boolean,
      default: true
    },
    
    showIndicator: {
      type: Boolean,
      default: true
    },
    
    // 自动展开
    autoExpand: {
      type: Boolean,
      default: false
    }
  },
  
  emits: ['island-click', 'state-changed', 'priority-changed'],
  
  setup(props, { emit }) {
    // 响应式状态
    const isExpanded = ref(false)
    const currentTime = ref('')
    const currentState = ref('idle')
    const timeInterval = ref(null)
    
    // 模拟状态数据
    const statusData = ref({
      navigation: {
        icon: 'fas fa-route',
        iconClass: 'navigation-active',
        text: '导航中',
        active: false,
        priority: 1,
        expandedContent: {
          title: '前往CBD',
          details: '剩余12分钟 · 畅通'
        }
      },
      music: {
        icon: 'fas fa-music',
        iconClass: 'music-active',
        text: '音乐播放',
        active: true,
        priority: 2,
        expandedContent: {
          title: '夜曲 - 周杰伦',
          details: '2:15 / 4:32'
        }
      },
      calls: {
        icon: 'fas fa-phone',
        iconClass: 'call-active',
        text: '通话中',
        active: false,
        priority: 0,
        expandedContent: {
          title: '通话中',
          details: '张三 · 05:23'
        }
      },
      notifications: {
        icon: 'fas fa-bell',
        iconClass: 'notification-active',
        text: '3条通知',
        active: false,
        priority: 3,
        expandedContent: {
          title: '新消息',
          details: '3条未读消息'
        }
      }
    })
    
    // 计算当前优先级最高的活跃项目
    const currentPriorityItem = computed(() => {
      const activeItems = Object.entries(statusData.value)
        .filter(([key, item]) => item.active)
        .sort((a, b) => a[1].priority - b[1].priority)
      
      if (activeItems.length > 0) {
        return activeItems[0][1]
      }
      
      // 默认显示音乐状态
      return statusData.value.music
    })
    
    // 扩展内容
    const expandedContent = computed(() => {
      return currentPriorityItem.value?.expandedContent || {
        title: 'AI-HMI',
        details: '智能车载系统'
      }
    })
    
    // 指示器状态
    const indicatorState = computed(() => {
      if (statusData.value.calls.active) return 'urgent'
      if (statusData.value.navigation.active) return 'active'
      if (statusData.value.music.active) return 'normal'
      return 'idle'
    })
    
    // 是否有操作
    const hasActions = computed(() => {
      return Object.values(statusData.value).some(item => item.active)
    })
    
    // 岛屿样式
    const islandStyles = computed(() => {
      return {
        '--island-primary': props.themeColors.primary,
        '--island-secondary': props.themeColors.secondary,
        '--island-background': props.themeColors.background,
        '--island-text': props.themeColors.text
      }
    })
    
    // 更新时间
    const updateTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    }
    
    // 处理点击事件
    const handleIslandClick = () => {
      if (props.size === 'compact') {
        // 紧凑模式下点击展开
        isExpanded.value = !isExpanded.value
      } else {
        // 正常模式下触发操作
        emit('island-click', {
          currentItem: currentPriorityItem.value,
          isExpanded: isExpanded.value,
          timestamp: new Date()
        })
      }
    }
    
    // 模拟状态变化
    const simulateStatusChanges = () => {
      // 模拟音乐播放状态
      setTimeout(() => {
        statusData.value.music.active = true
        currentState.value = 'music'
      }, 2000)
      
      // 模拟导航启动
      setTimeout(() => {
        statusData.value.navigation.active = true
        currentState.value = 'navigation'
      }, 5000)
      
      // 模拟通知
      setTimeout(() => {
        statusData.value.notifications.active = true
        statusData.value.notifications.text = '1条新消息'
      }, 8000)
    }
    
    // 生命周期
    onMounted(() => {
      updateTime()
      timeInterval.value = setInterval(updateTime, 1000)
      
      if (props.autoExpand) {
        setTimeout(() => {
          isExpanded.value = true
        }, 1000)
      }
      
      // 启动状态模拟
      simulateStatusChanges()
    })
    
    onUnmounted(() => {
      if (timeInterval.value) {
        clearInterval(timeInterval.value)
      }
    })
    
    return {
      isExpanded,
      currentTime,
      currentState,
      currentPriorityItem,
      expandedContent,
      indicatorState,
      hasActions,
      islandStyles,
      handleIslandClick
    }
  }
}
</script>

<style scoped>
.dynamic-island {
  position: relative;
  background: var(--island-background, rgba(0, 0, 0, 0.8));
  border-radius: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
  min-height: 40px;
  display: flex;
  align-items: center;
}

/* 尺寸变体 */
.size-compact {
  padding: 8px 16px;
  min-width: 120px;
}

.size-normal {
  padding: 10px 20px;
  min-width: 200px;
}

.size-expanded {
  padding: 12px 24px;
  min-width: 300px;
}

/* 扩展状态 */
.dynamic-island.expanded {
  min-width: 400px;
  border-radius: 20px;
}

/* 内容布局 */
.island-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 15px;
}

.island-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.island-center {
  flex: 1;
  text-align: center;
}

.island-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 状态图标 */
.status-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.3s ease;
}

.navigation-active { background: #007AFF; color: white; }
.music-active { background: #FF3B30; color: white; }
.call-active { background: #34C759; color: white; }
.notification-active { background: #FF9500; color: white; }

/* 状态文本 */
.status-text {
  font-size: 12px;
  font-weight: 500;
  color: var(--island-text, #ffffff);
  white-space: nowrap;
}

/* 扩展内容 */
.expanded-content {
  animation: fadeIn 0.3s ease;
}

.content-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--island-text, #ffffff);
  margin-bottom: 2px;
}

.content-details {
  font-size: 11px;
  color: var(--island-text, #ffffff);
  opacity: 0.7;
}

/* 时间显示 */
.current-time {
  font-size: 12px;
  font-weight: 600;
  color: var(--island-text, #ffffff);
  font-variant-numeric: tabular-nums;
}

/* 操作指示器 */
.action-indicator {
  font-size: 10px;
  color: var(--island-text, #ffffff);
  opacity: 0.6;
}

/* 活动指示器 */
.activity-indicator {
  position: absolute;
  top: 6px;
  right: 6px;
}

.indicator-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.indicator-dot.idle { background: #8E8E93; }
.indicator-dot.normal { background: #34C759; animation: pulse 2s infinite; }
.indicator-dot.active { background: #007AFF; animation: pulse 1s infinite; }
.indicator-dot.urgent { background: #FF3B30; animation: blink 0.5s infinite; }

/* 悬停效果 */
.dynamic-island:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}
</style>
