<template>
  <div class="dynamic-island" :class="[`theme-${theme}`, `mode-${currentMode}`]" @click="handleIslandClick">
    <!-- 主显示区域 -->
    <div class="island-main">
      <!-- 左侧信息区 -->
      <div class="info-section">
        <!-- 场景图标和名称 -->
        <div class="scene-info">
          <i :class="sceneIcon" class="scene-icon"></i>
          <span class="scene-name">{{ sceneName }}</span>
        </div>
        
        <!-- 时间信息 -->
        <div class="time-info">
          <span class="current-time">{{ currentTime }}</span>
          <span class="current-date">{{ currentDate }}</span>
        </div>
        
        <!-- 状态信息 -->
        <div class="status-info" v-if="showStatus">
          <div class="status-item" v-for="(status, index) in statusItems" :key="index">
            <i :class="status.icon"></i>
            <span>{{ status.text }}</span>
          </div>
        </div>
      </div>
      
      <!-- 中间功能区 -->
      <div class="function-section">
        <!-- 导航信息 -->
        <div class="navigation-info" v-if="currentScene.showNavigation">
          <div class="nav-destination">
            <i class="fas fa-location-dot"></i>
            <span>{{ navigationInfo.destination }}</span>
          </div>
          <div class="nav-eta">
            <i class="fas fa-clock"></i>
            <span>{{ navigationInfo.eta }}</span>
          </div>
          <div class="nav-distance">
            <i class="fas fa-route"></i>
            <span>{{ navigationInfo.distance }}</span>
          </div>
        </div>
        
        <!-- 音乐控制 -->
        <div class="music-control" v-if="currentScene.showMusic">
          <div class="music-info">
            <div class="music-album">
              <img :src="currentSong?.albumArt || '/assets/default-album.png'" alt="Album Art">
            </div>
            <div class="music-details">
              <div class="song-title">{{ currentSong?.title || '未播放' }}</div>
              <div class="song-artist">{{ currentSong?.artist || '未知艺术家' }}</div>
            </div>
          </div>
          <div class="music-controls">
            <button @click.stop="previousSong" class="control-btn">
              <i class="fas fa-backward"></i>
            </button>
            <button @click.stop="togglePlay" class="control-btn play-btn">
              <i :class="isPlaying ? 'fas fa-pause' : 'fas fa-play'"></i>
            </button>
            <button @click.stop="nextSong" class="control-btn">
              <i class="fas fa-forward"></i>
            </button>
          </div>
        </div>
        
        <!-- 天气信息 -->
        <div class="weather-info" v-if="currentScene.showWeather">
          <div class="weather-current">
            <i :class="weatherIcon" class="weather-icon"></i>
            <div class="weather-temp">{{ weatherInfo.temperature }}°C</div>
          </div>
          <div class="weather-details">
            <div class="weather-desc">{{ weatherInfo.description }}</div>
            <div class="weather-location">{{ weatherInfo.location }}</div>
          </div>
        </div>
      </div>
      
      <!-- 右侧控制区 -->
      <div class="control-section">
        <!-- 快速设置 -->
        <div class="quick-settings">
          <button 
            v-for="setting in quickSettings" 
            :key="setting.id"
            @click.stop="handleQuickSetting(setting)"
            class="setting-btn"
            :title="setting.label"
          >
            <i :class="setting.icon"></i>
            <span class="setting-indicator" v-if="setting.active"></span>
          </button>
        </div>
        
        <!-- 通知指示器 -->
        <div class="notification-indicator" v-if="hasNotifications" @click.stop="handleNotifications">
          <i class="fas fa-bell"></i>
          <span class="notification-count">{{ notificationCount }}</span>
        </div>
        
        <!-- 用户头像 -->
        <div class="user-avatar" @click.stop="handleUserClick">
          <img :src="userAvatar" alt="User Avatar">
        </div>
      </div>
    </div>
    
    <!-- 扩展区域（点击时展开） -->
    <div v-if="isExpanded" class="island-expanded" @click.stop>
      <div class="expanded-content">
        <!-- 扩展内容根据当前场景动态显示 -->
        <component :is="expandedComponent" v-if="expandedComponent" />
        
        <!-- 默认扩展内容 -->
        <div v-else class="default-expanded">
          <div class="expanded-section">
            <h4>快捷功能</h4>
            <div class="quick-actions">
              <button v-for="action in expandedActions" :key="action.id" @click="handleExpandedAction(action)">
                <i :class="action.icon"></i>
                <span>{{ action.label }}</span>
              </button>
            </div>
          </div>
          
          <div class="expanded-section">
            <h4>系统状态</h4>
            <div class="system-status">
              <div class="status-item" v-for="status in systemStatus" :key="status.id">
                <i :class="status.icon"></i>
                <span>{{ status.label }}: {{ status.value }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'

export default {
  name: 'DynamicIsland',
  props: {
    currentScene: {
      type: Object,
      required: true
    },
    themeColors: {
      type: Object,
      default: null
    }
  },
  emits: ['island-click', 'quick-setting', 'music-control', 'expanded-action'],
  setup(props, { emit }) {
    const isExpanded = ref(false)
    const isPlaying = ref(false)
    const currentTime = ref('')
    const currentDate = ref('')
    
    // 主题
    const theme = computed(() => props.currentScene?.theme || 'light')
    
    // 当前模式
    const currentMode = computed(() => {
      const sceneId = props.currentScene?.id || 'default'
      if (sceneId.includes('family')) return 'family'
      if (sceneId.includes('focus')) return 'focus'
      if (sceneId.includes('entertainment')) return 'entertainment'
      if (sceneId.includes('minimal')) return 'minimal'
      return 'default'
    })
    
    // 场景信息
    const sceneIcon = computed(() => {
      const iconMap = {
        family: 'fas fa-child',
        focus: 'fas fa-briefcase',
        entertainment: 'fas fa-couch',
        minimal: 'fas fa-moon',
        default: 'fas fa-home'
      }
      return iconMap[currentMode.value] || 'fas fa-circle'
    })
    
    const sceneName = computed(() => props.currentScene?.name || '默认场景')
    
    // 状态显示
    const showStatus = computed(() => {
      return currentMode.value !== 'minimal'
    })
    
    const statusItems = computed(() => {
      const baseStatus = [
        { icon: 'fas fa-wifi', text: '在线' },
        { icon: 'fas fa-battery-three-quarters', text: '85%' }
      ]
      
      if (currentMode.value === 'family') {
        baseStatus.push({ icon: 'fas fa-users', text: '家庭模式' })
      } else if (currentMode.value === 'focus') {
        baseStatus.push({ icon: 'fas fa-car', text: '驾驶中' })
      }
      
      return baseStatus
    })
    
    // 导航信息
    const navigationInfo = ref({
      destination: '公司',
      eta: '25分钟',
      distance: '12公里'
    })
    
    // 音乐信息
    const currentSong = ref({
      title: '清晨的阳光',
      artist: 'AI音乐',
      albumArt: '/assets/default-album.png'
    })
    
    // 天气信息
    const weatherInfo = ref({
      temperature: 22,
      description: '晴朗',
      location: '北京'
    })
    
    const weatherIcon = computed(() => {
      const desc = weatherInfo.value.description
      if (desc.includes('晴')) return 'fas fa-sun'
      if (desc.includes('云')) return 'fas fa-cloud'
      if (desc.includes('雨')) return 'fas fa-cloud-rain'
      return 'fas fa-sun'
    })
    
    // 快速设置
    const quickSettings = ref([
      { id: 'wifi', icon: 'fas fa-wifi', label: 'WiFi', active: true },
      { id: 'bluetooth', icon: 'fas fa-bluetooth', label: '蓝牙', active: false },
      { id: 'brightness', icon: 'fas fa-sun', label: '亮度', active: true },
      { id: 'volume', icon: 'fas fa-volume-up', label: '音量', active: true }
    ])
    
    // 通知
    const hasNotifications = ref(true)
    const notificationCount = ref(3)
    
    // 用户信息
    const userAvatar = ref('/assets/default-avatar.png')
    
    // 扩展组件
    const expandedComponent = computed(() => {
      // 根据场景返回不同的扩展组件
      return null // 暂时返回null，使用默认扩展内容
    })
    
    // 扩展操作
    const expandedActions = ref([
      { id: 'navigation', icon: 'fas fa-location-dot', label: '导航' },
      { id: 'music', icon: 'fas fa-music', label: '音乐' },
      { id: 'phone', icon: 'fas fa-phone', label: '电话' },
      { id: 'messages', icon: 'fas fa-message', label: '消息' },
      { id: 'settings', icon: 'fas fa-cog', label: '设置' }
    ])
    
    // 系统状态
    const systemStatus = ref([
      { id: 'battery', icon: 'fas fa-battery-three-quarters', label: '电池', value: '85%' },
      { id: 'storage', icon: 'fas fa-hdd', label: '存储', value: '45GB' },
      { id: 'memory', icon: 'fas fa-microchip', label: '内存', value: '4.2GB' },
      { id: 'temperature', icon: 'fas fa-thermometer-half', label: '温度', value: '42°C' }
    ])
    
    // 处理点击
    const handleIslandClick = () => {
      isExpanded.value = !isExpanded.value
      emit('island-click', {
        expanded: isExpanded.value,
        mode: currentMode.value,
        scene: props.currentScene
      })
    }
    
    // 处理快速设置
    const handleQuickSetting = (setting) => {
      setting.active = !setting.active
      emit('quick-setting', {
        setting: setting.id,
        active: setting.active,
        scene: props.currentScene
      })
    }
    
    // 音乐控制
    const togglePlay = () => {
      isPlaying.value = !isPlaying.value
      emit('music-control', {
        action: 'toggle',
        playing: isPlaying.value,
        song: currentSong.value
      })
    }
    
    const previousSong = () => {
      emit('music-control', {
        action: 'previous',
        playing: isPlaying.value,
        song: currentSong.value
      })
    }
    
    const nextSong = () => {
      emit('music-control', {
        action: 'next',
        playing: isPlaying.value,
        song: currentSong.value
      })
    }
    
    // 处理通知
    const handleNotifications = () => {
      console.log('打开通知中心')
    }
    
    // 处理用户点击
    const handleUserClick = () => {
      console.log('打开用户菜单')
    }
    
    // 处理扩展操作
    const handleExpandedAction = (action) => {
      emit('expanded-action', {
        action: action.id,
        scene: props.currentScene
      })
    }
    
    // 更新时间
    const updateTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
      currentDate.value = now.toLocaleDateString('zh-CN', { 
        month: '2-digit', 
        day: '2-digit' 
      })
    }
    
    // 模拟数据更新
    const simulateDataUpdate = () => {
      // 模拟导航信息更新
      if (Math.random() > 0.7) {
        const eta = Math.floor(Math.random() * 30) + 10
        navigationInfo.value.eta = `${eta}分钟`
      }
      
      // 模拟天气信息更新
      if (Math.random() > 0.9) {
        const temp = Math.floor(Math.random() * 15) + 15
        weatherInfo.value.temperature = temp
      }
    }
    
    onMounted(() => {
      updateTime()
      
      // 每秒更新时间
      const timeInterval = setInterval(updateTime, 1000)
      
      // 每30秒模拟数据更新
      const dataInterval = setInterval(simulateDataUpdate, 30000)
      
      onUnmounted(() => {
        clearInterval(timeInterval)
        clearInterval(dataInterval)
      })
    })
    
    return {
      isExpanded,
      isPlaying,
      currentTime,
      currentDate,
      theme,
      currentMode,
      sceneIcon,
      sceneName,
      showStatus,
      statusItems,
      navigationInfo,
      currentSong,
      weatherInfo,
      weatherIcon,
      quickSettings,
      hasNotifications,
      notificationCount,
      userAvatar,
      expandedComponent,
      expandedActions,
      systemStatus,
      handleIslandClick,
      handleQuickSetting,
      togglePlay,
      previousSong,
      nextSong,
      handleNotifications,
      handleUserClick,
      handleExpandedAction
    }
  }
}
</script>

<style scoped>
.dynamic-island {
  width: 100%;
  height: 100%;
  background: var(--background, rgba(255, 255, 255, 0.1));
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid var(--border-color, rgba(255, 255, 255, 0.2));
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.dynamic-island:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

/* 主显示区域 */
.island-main {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 20px;
  gap: 20px;
}

/* 信息区域 */
.info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.scene-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.scene-icon {
  font-size: 16px;
  color: var(--primary-color, #4a90e2);
}

.scene-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color, #ffffff);
}

.time-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.current-time {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-color, #ffffff);
}

.current-date {
  font-size: 12px;
  color: var(--text-color, #ffffff);
  opacity: 0.8;
}

.status-info {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: var(--text-color, #ffffff);
  opacity: 0.8;
}

.status-item i {
  font-size: 10px;
}

/* 功能区域 */
.function-section {
  flex: 2;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 导航信息 */
.navigation-info {
  display: flex;
  gap: 15px;
  align-items: center;
}

.nav-destination,
.nav-eta,
.nav-distance {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: var(--text-color, #ffffff);
}

/* 音乐控制 */
.music-control {
  display: flex;
  align-items: center;
  gap: 15px;
  width: 100%;
}

.music-info {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.music-album {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  overflow: hidden;
}

.music-album img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.music-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.song-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-color, #ffffff);
}

.song-artist {
  font-size: 10px;
  color: var(--text-color, #ffffff);
  opacity: 0.8;
}

.music-controls {
  display: flex;
  gap: 8px;
}

.control-btn {
  width: 30px;
  height: 30px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: var(--text-color, #ffffff);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: var(--primary-color, #4a90e2);
  transform: scale(1.1);
}

.play-btn {
  width: 36px;
  height: 36px;
  background: var(--primary-color, #4a90e2);
}

/* 天气信息 */
.weather-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.weather-current {
  display: flex;
  align-items: center;
  gap: 8px;
}

.weather-icon {
  font-size: 24px;
  color: var(--primary-color, #4a90e2);
}

.weather-temp {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color, #ffffff);
}

.weather-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.weather-desc {
  font-size: 12px;
  color: var(--text-color, #ffffff);
}

.weather-location {
  font-size: 10px;
  color: var(--text-color, #ffffff);
  opacity: 0.8;
}

/* 控制区域 */
.control-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 10px;
}

.quick-settings {
  display: flex;
  gap: 5px;
}

.setting-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-color, #ffffff);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
}

.setting-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.setting-indicator {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #4CAF50;
}

.notification-indicator {
  position: relative;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-color, #ffffff);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.notification-indicator:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.notification-count {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #ff4444;
  color: white;
  font-size: 10px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.1);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 扩展区域 */
.island-expanded {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--background, rgba(255, 255, 255, 0.1));
  backdrop-filter: blur(20px);
  border-radius: 0 0 20px 20px;
  border: 1px solid var(--border-color, rgba(255, 255, 255, 0.2));
  border-top: none;
  margin-top: 10px;
  z-index: 1000;
  max-height: 400px;
  overflow-y: auto;
}

.expanded-content {
  padding: 20px;
}

.default-expanded {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.expanded-section h4 {
  margin: 0 0 10px 0;
  color: var(--text-color, #ffffff);
  font-size: 14px;
  font-weight: 600;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 10px;
}

.quick-actions button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  padding: 10px;
  border: none;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-color, #ffffff);
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-actions button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.quick-actions button i {
  font-size: 20px;
}

.quick-actions button span {
  font-size: 12px;
}

.system-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.system-status .status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--text-color, #ffffff);
}

/* 主题样式 */
.theme-light {
  --primary-color: #4a90e2;
  --secondary-color: #7ed321;
  --background: rgba(255, 255, 255, 0.9);
  --border-color: rgba(255, 255, 255, 0.3);
  --text-color: #333333;
}

.theme-dark {
  --primary-color: #2c3e50;
  --secondary-color: #3498db;
  --background: rgba(0, 0, 0, 0.8);
  --border-color: rgba(255, 255, 255, 0.2);
  --text-color: #ffffff;
}

.theme-warm {
  --primary-color: #e74c3c;
  --secondary-color: #f39c12;
  --background: rgba(255, 193, 7, 0.1);
  --border-color: rgba(255, 193, 7, 0.3);
  --text-color: #2c3e50;
}

.theme-calm {
  --primary-color: #3498db;
  --secondary-color: #2ecc71;
  --background: rgba(52, 152, 219, 0.1);
  --border-color: rgba(52, 152, 219, 0.3);
  --text-color: #2c3e50;
}

/* 模式样式 */
.mode-family {
  --accent-color: #ff6b6b;
}

.mode-focus {
  --accent-color: #4ecdc4;
}

.mode-entertainment {
  --accent-color: #45b7d1;
}

.mode-minimal {
  --accent-color: #96ceb4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .island-main {
    padding: 0 10px;
    gap: 10px;
  }
  
  .info-section {
    flex: 0.8;
  }
  
  .function-section {
    flex: 1;
  }
  
  .control-section {
    flex: 0.8;
  }
  
  .scene-name {
    font-size: 12px;
  }
  
  .current-time {
    font-size: 16px;
  }
  
  .music-info {
    display: none;
  }
  
  .weather-details {
    display: none;
  }
  
  .quick-settings {
    display: none;
  }
  
  .user-avatar {
    width: 28px;
    height: 28px;
  }
  
  .expanded-content {
    padding: 15px;
  }
  
  .quick-actions {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 动画效果 */
.dynamic-island {
  animation: islandAppear 0.5s ease-out;
}

@keyframes islandAppear {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.island-expanded {
  animation: expandDown 0.3s ease-out;
}

@keyframes expandDown {
  from {
    transform: scaleY(0);
    opacity: 0;
  }
  to {
    transform: scaleY(1);
    opacity: 1;
  }
}
</style>